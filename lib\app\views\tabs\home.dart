import 'dart:math';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/controllers/main_controller.dart';
import 'package:zamin_phone/app/controllers/controllers/products_controller.dart';
import 'package:zamin_phone/app/models/data.dart';
import 'package:zamin_phone/app/views/widgets/cached_image.dart';
import 'package:zamin_phone/app/views/widgets/home/<USER>';
import 'package:zamin_phone/app/views/widgets/home/<USER>';
import 'package:zamin_phone/app/views/widgets/home/<USER>';
import 'package:zamin_phone/app/views/widgets/product_wedgit.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/configs/constants.dart';
import 'package:zamin_phone/configs/text_style.dart';
import '../../../main.dart';
import '../screens/sub_categories.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  MainController mainController = Get.find();
  ProductsController productsController = Get.find();
  int currentIndex = 0;

  @override
  void initState() {
    //ProductApi().getOrders();
    super.initState();
    productsController.products.shuffle(Random());
  }

  @override
  Widget build(BuildContext context) {
    final slider = productsController.slider;
    print(prefs!.getString('token'));
    return Container(
      padding: const EdgeInsets.only(top: 20, left: 15, right: 15),
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            CarouselSlider(
                items: slider.map((i) {
                  return Builder(
                    builder: (BuildContext context) {
                      return Container(
                          width: MediaQuery.of(context).size.width,
                          margin: const EdgeInsets.symmetric(horizontal: 5.0),
                          decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                    spreadRadius: 3,
                                    blurRadius: 2,
                                    color: Colors.grey.withValues(alpha: 0.2))
                              ],
                              color: const Color(0xFFF7F9FB),
                              borderRadius: BorderRadius.circular(10)),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10),
                            child: Image(
                              image: CachedImage(
                                      imageLink:
                                          Constants().imagerUrl + i.image)
                                  .cachedImage(),
                              fit: BoxFit.fill,
                            ),
                          ));
                    },
                  );
                }).toList(),
                options: CarouselOptions(
                    viewportFraction: 1,
                    height: 175,
                    autoPlay: true,
                    onPageChanged: (v, _) {
                      setState(() {
                        currentIndex = v;
                      });
                    })),
            carouselIndicatior(productsController.slider.length),
            const SizedBox(
              height: 30,
            ),
            Directionality(
              textDirection: prefs!.getString('lang') == 'en'
                  ? TextDirection.ltr
                  : TextDirection.rtl,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Categories'.tr,
                    style: TextStylesManagment.primarySmall,
                  ),
                  GestureDetector(
                    onTap: () => mainController.changeCurrentIndex(1),
                    child: Text(
                      'see all'.tr,
                      style: TextStylesManagment.primarySmall,
                    ),
                  )
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            categoriesMethod(),
            const SizedBox(
              height: 30,
            ),
            Column(
              children: productsController.offerCategories
                  .asMap()
                  .entries
                  .map((entry) {
                int index = entry.key + 1;
                var offer = entry.value;
                return Padding(
                  padding: const EdgeInsets.only(top: 5.0, bottom: 15),
                  child: _offerMethod(offer, index),
                );
              }).toList(),
            ),
            const SizedBox(
              height: 25,
            )
          ],
        ),
      ),
    );
  }

  customStatisticsMethod(context, type) {
    final products = productsController.products.take(8).toList();
    return SizedBox(
      height: Get.width * 0.2,
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemCount: products.length,
        itemBuilder: (context, index) {
          return ProductWedgit(products[index]);
        },
      ),
    );
  }

  SizedBox categoriesMethod() {
    return SizedBox(
      height: Get.width * 0.18,
      child: ListView.builder(
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          physics: const ScrollPhysics(),
          itemCount: productsController.categories.length < 8
              ? productsController.categories.length
              : 8,
          itemBuilder: (context, index) {
            final cat = productsController.categories[index];
            return GestureDetector(
              onTap: () => Get.to(Subcategories(cat.id, cat.name)),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                width: Get.width * 0.15,
                height: Get.width * 0.15,
                decoration: BoxDecoration(
                    border:
                        Border.all(width: 1, color: ColorManagement.primary),
                    shape: BoxShape.circle,
                    image: DecorationImage(
                        image: CachedImage(
                                imageLink: Constants().imagerUrl + cat.image!)
                            .cachedImage(),
                        fit: BoxFit.contain)),
              ),
            );
          }),
    );
  }

  carouselIndicatior(length) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          for (int i = 0; i < length; i++)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 3),
              width: currentIndex == i ? 40 : 10,
              height: 10,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: currentIndex == i
                    ? Colors.orange
                    : ColorManagement.lightPrimary,
              ),
            )
        ],
      ),
    );
  }

  Widget _offerMethod(OfferCategory offer, int index) {
    Widget child;
    switch (index % 3) {
      case 0:
        child = checkOferAvailable(offer.offers, productsController.products) &&
                offer.offers.isNotEmpty
            ? DailyOffers(
                offer.offers,
                productsController.products,
                productsController.categories,
                productsController.categorySubs,
                offer.name)
            : Container();
        break;
      case 1:
        child = checkOferAvailable(offer.offers, productsController.products) &&
                offer.offers.isNotEmpty
            ? BestSellerWidget(
                offer.offers,
                productsController.products,
                productsController.categories,
                productsController.categorySubs,
                offer.name)
            : Container();
        break;
      default:
        child = checkOferAvailable(offer.offers, productsController.products) &&
                offer.offers.isNotEmpty
            ? DiscountWidget(
                offer.offers,
                productsController.products,
                productsController.categories,
                productsController.categorySubs,
                offer.name)
            : Container();
    }
    return child;
  }

  bool checkOferAvailable(List<Offer> offers, List<Product> products) {
    bool isFound = offers.every(
        (offer) => products.any((product) => product.id == offer.productId));

    return isFound;
  }
}
