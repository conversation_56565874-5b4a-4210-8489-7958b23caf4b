import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/controllers/notification.dart';
import 'package:zamin_phone/app/views/screens/notifications.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/main.dart';

class AppBars {
  NotificationController notificationController = Get.find();
  customAppBar(title) {
    final isLogined = prefs!.getBool("isLogined") ?? false;

    return AppBar(
      leadingWidth: double.infinity,
      leading: Padding(
        padding: const EdgeInsets.only(left: 5.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "welcome back".tr,
              style: const TextStyle(
                  color: Colors.orange, fontWeight: FontWeight.bold),
            ),
            Text(isLogined ? "${prefs!.getString("username")}" : "",
                style: const TextStyle(color: Colors.orange)),
          ],
        ),
      ),
      title: Text(
        title,
        style:
            const TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
      ),
      actions: [
        Stack(
          children: [
            IconButton(
              icon: const Icon(Icons.notifications, color: Colors.orange),
              onPressed: () {
                Get.to(() => const NotificationsScreen());
                notificationController.resetCount();
              },
            ),
            Positioned(
              right: 11,
              top: 11,
              child: Obx(
                () => notificationController.notificationCount > 0
                    ? Container(
                        width: 15, height: 15,
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        // constraints: const BoxConstraints(
                        //   maxWidth: 15,
                        //   maxHeight: 15,
                        // ),
                        child: Text(
                          '${notificationController.notificationCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : const SizedBox(),
              ),
            ),
          ],
        ),
      ],
      centerTitle: true,
      backgroundColor: ColorManagement.secondoryColor,
    );
  }

  screensAppBar(title) {
    return AppBar(
      foregroundColor: Colors.orange,
      title: Text(
        title,
        style:
            const TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
      ),
      centerTitle: true,
      backgroundColor: ColorManagement.secondoryColor,
    );
  }
}
