import 'package:barcode_widget/barcode_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:zamin_phone/app/controllers/services/auth.dart';
import 'package:zamin_phone/app/views/auth/register.dart';
import 'package:zamin_phone/app/views/screens/profile/faq.dart';
import 'package:zamin_phone/app/views/screens/profile/privacy.dart';
import 'package:zamin_phone/app/views/widgets/textfield.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/configs/text_style.dart';

import '../../../main.dart';
import '../../controllers/controllers/main_controller.dart';
import '../../controllers/languages/locale_controller.dart';
import '../screens/profile/refund.dart';
import '../screens/profile/terms.dart';
import '../screens/user_orders.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  LocaleController localController = Get.find();
  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Directionality(
          textDirection: prefs!.getString('lang') == 'en'
              ? TextDirection.ltr
              : TextDirection.rtl,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (prefs!.getBool("isLogined")!) _buildAccountInfo(),
              const SizedBox(height: 20),
              _buildSettingsSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountInfo() {
    final username = prefs!.getString("username");
    final id = prefs!.getString("id");
    final phone = AuthApi().reformatPhoneNumber(prefs!.getString("phone"), 2);
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.orange,
            ),
            child: const Icon(
              Icons.person,
              size: 40,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  username ?? '',
                  style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  phone,
                  style: const TextStyle(color: Colors.black),
                ),
              ],
            ),
          ),
          BarcodeWidget(
            backgroundColor: Colors.white,
            width: 120,
            barcode: Barcode.code128(),
            data: "$id",
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(BuildContext context) {
    String phoneNumber = "+9647506666001"; // Replace with your phone number
    String message = "سلاڤ و رێز";
    bool? isLogined = prefs!.getBool("isLogined");
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Settings'.tr,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 10),
          if (isLogined!)
            _buildSettingsOption(context,
                icon: Icons.inventory_2,
                title: 'My Orders'.tr,
                onTap: () => _loadUserOrderScreen()),
          _buildSettingsOption(context,
              icon: Icons.language,
              title: 'Language'.tr,
              onTap: () => _selectLanguage(context)),
          if (prefs!.getBool("isLogined")!)
            _buildSettingsOption(context,
                icon: Icons.key,
                title: 'Change Password'.tr,
                onTap: () => _changePassword()),
          _buildSettingsOption(context,
              icon: Icons.comment,
              title: 'Customer Support'.tr,
              onTap: () => _support(phoneNumber, message)),
          _buildSettingsOption(context,
              icon: Icons.question_answer,
              title: 'FAQs'.tr,
              onTap: () => _faq()),
          _buildSettingsOption(context,
              icon: Icons.document_scanner,
              title: 'Terms and Conditions'.tr,
              onTap: () => _term()),
          _buildSettingsOption(context,
              icon: Icons.privacy_tip,
              title: 'Privacy Policy'.tr,
              onTap: () => _privacy()),
          _buildSettingsOption(context,
              icon: Icons.money,
              title: 'Refund Policy'.tr,
              onTap: () => _refund()),
          if (prefs!.getBool("isLogined")!)
            _buildSettingsOption(
              context,
              icon: Icons.delete,
              iconColor: Colors.white,
              title: 'Delet Account'.tr,
              onTap: () => _deleteAccount(),
            ),
          _buildSettingsOption(context,
              icon: Icons.power_settings_new,
              title: 'Sign out'.tr,
              iconColor: Colors.white,
              onTap: () => _signOut()),
        ],
      ),
    );
  }

  Widget _buildSettingsOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? trailingText,
    Color iconColor = Colors.white,
    Future<void> Function()? onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3.0),
      child: ListTile(
        leading: CircleAvatar(
          radius: 20,
          backgroundColor: iconColor.withValues(alpha: 0.2),
          child: Icon(icon, color: iconColor),
        ),
        title: Text(
          title,
          style: const TextStyle(
              fontSize: 14, fontWeight: FontWeight.w500, color: Colors.white),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (trailingText != null)
              Text(
                trailingText,
                style: const TextStyle(color: Colors.grey),
              ),
            const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
          ],
        ),
        onTap: onTap != null
            ? () async {
                await onTap(); // Call the async function
              }
            : null,
      ),
    );
  }

  // ====================== Change Password ======================
  _changePassword() async {
    final TextEditingController currentPasswordController =
        TextEditingController();
    final TextEditingController newPasswordController = TextEditingController();
    final TextEditingController confirmPasswordController =
        TextEditingController();
    return showModalBottomSheet(
      context: context,
      isScrollControlled:
          true, // This allows the modal to move up with the keyboard
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(27.5),
                  topRight: Radius.circular(27.5)),
              // border:
              //     Border.all(color: ColorManagement.lightPrimary, width: 0.2),
              // boxShadow: [
              //   BoxShadow(
              //       spreadRadius: 3,
              //       blurRadius: 2,
              //       color: Colors.grey.withOpacity(0.2))
              // ],
              color: ColorManagement.secondoryColor,
            ),
            height: 700,
            width: Get.width,
            padding: const EdgeInsets.all(20),
            child: SingleChildScrollView(
              child: Column(
                //mainAxisSize: MainAxisSize.max, // Adjust size based on content
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(
                    height: 15,
                  ),
                  _buildPasswordField(
                      'Current Password', currentPasswordController, true),
                  _buildPasswordField(
                      'New Password', newPasswordController, true),
                  _buildPasswordField(
                      'Confirm New Password', confirmPasswordController, true),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () async {
                            await AuthApi().changePassword(
                                currentPasswordController.text,
                                newPasswordController.text);
                            _signOut();
                          },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                              width: 1.0,
                              color: Colors.orange,
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            'Save'.tr,
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                              width: 1.0,
                              color: Colors.orange,
                            ),
                            backgroundColor: ColorManagement.primary,
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text('Cancel'.tr,
                              style: const TextStyle(color: Colors.white)),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPasswordField(
      String label, TextEditingController controller, bool obscureText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          obscureText: obscureText,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            filled: true,
            fillColor: ColorManagement.primary,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[200]!, width: 0.0),
            ),
          ),
        ),
        const SizedBox(height: 4),
      ],
    );
  }

  //======================= Select Language ======================
  _deleteAccount() {
    TextEditingController passWordController = TextEditingController();
    PasswordFieldController deletePasswordController =
        PasswordFieldController();
    return showModalBottomSheet(
        context: context,
        builder: (context) {
          return Container(
            decoration: BoxDecoration(
                color: ColorManagement.secondoryColor,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(27.5),
                    topRight: Radius.circular(27.5))),
            height: 250,
            width: Get.width,
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  'Are you sure to delete your account'.tr,
                  style: const TextStyle(fontSize: 18, color: Colors.white),
                ),
                const SizedBox(height: 20),
                textField(passWordController, "Password",
                    TextInputType.visiblePassword,
                    passwordController: deletePasswordController),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    GestureDetector(
                      onTap: () async {
                        final res = await AuthApi()
                            .removeAccount(passWordController.text);
                        print(res);
                        deletePasswordController.dispose();
                        Get.offAll(() => const AuthScreen());
                      },
                      child: Text(
                        'Yes'.tr,
                        style: const TextStyle(
                            color: Colors.red,
                            fontSize: 16,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        deletePasswordController.dispose();
                        Navigator.pop(context);
                      },
                      child: Text('No'.tr,
                          style: const TextStyle(
                              color: Colors.green,
                              fontSize: 16,
                              fontWeight: FontWeight.bold)),
                    )
                  ],
                )
              ],
            ),
          );
        });
  }

  //======================= Select Language ======================
  _selectLanguage(context) {
    return showModalBottomSheet(
        context: context,
        builder: (context) {
          return Container(
            decoration: BoxDecoration(
                color: ColorManagement.secondoryColor,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(27.5),
                    topRight: Radius.circular(27.5))),
            height: 150,
            width: Get.width,
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  'Select Language'.tr,
                  style: TextStylesManagment.medTitle,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          prefs!.setString("lang", "ku");
                          localController.changeLanguage("ku");
                        });
                        Navigator.pop(context);
                      },
                      child: Text(
                        "Badini".tr,
                        style: TextStylesManagment.productTitle,
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          prefs!.setString("lang", "so");
                          localController.changeLanguage("so");
                        });
                        Navigator.pop(context);
                      },
                      child: Text("Sorani".tr,
                          style: TextStylesManagment.productTitle),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          prefs!.setString("lang", "en");
                          localController.changeLanguage("en");
                        });
                        Navigator.pop(context);
                      },
                      child: Text("English".tr,
                          style: TextStylesManagment.productTitle),
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          prefs!.setString("lang", "ar");
                          localController.changeLanguage("ar");
                        });
                        Navigator.pop(context);
                      },
                      child: Text("Arabic".tr,
                          style: TextStylesManagment.productTitle),
                    ),
                  ],
                ),
              ],
            ),
          );
        });
  }

  _signOut() async {
    signOutGoogle();
    final controller = Get.find<MainController>();
    controller.changeCurrentIndex(0);
    await prefs!.clear();
    Get.offAll(() => const AuthScreen());
  }

  _loadUserOrderScreen() {
    Get.to(() => const UserOrders());
  }

  _privacy() {
    Get.to(() => const PrivacyPolicyScreen());
  }

  _faq() {
    Get.to(() => FAQPage());
  }

  _support(phoneNumber, message) async {
    final Uri whatsappUri = Uri.parse(
        "https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}");
    await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
  }

  _term() {
    Get.to(() => const TermsAndConditionsPage());
  }

  _refund() {
    Get.to(() => RefundPolicyPage());
  }

  // _updateLocation() {
  //   AuthApi().determinePosition();
  //   final lat = prefs!.getDouble('Latitude');
  //   final long = prefs!.getDouble('Longitude');
  //   Get.snackbar("Success", "Your New Location Updated");
  // }

  signOutGoogle() {
    const List<String> scopes = <String>[
      'email',
      'https://www.googleapis.com/auth/contacts.readonly',
    ];
    GoogleSignIn _googleSignIn = GoogleSignIn(
      // Optional clientId
      // clientId: 'your-client_id.apps.googleusercontent.com',
      scopes: scopes,
    );
    _googleSignIn.disconnect();
  }
}
