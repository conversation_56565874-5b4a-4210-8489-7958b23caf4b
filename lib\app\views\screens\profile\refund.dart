import 'package:flutter/material.dart';
import 'package:zamin_phone/app/views/screens/profile/docs/refunds.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/main.dart';

class RefundPolicyPage extends StatelessWidget {
  const RefundPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    List<Map<String, String>> refundPolicies;
    final lang = prefs!.getString('lang');
    switch (lang) {
      case "en":
        refundPolicies = Refunds().en;
      case "ar":
        refundPolicies = Refunds().ar;
        break;
      case "so":
        refundPolicies = Refunds().so;
        break;
      default:
        refundPolicies = Refunds().ku;
    }
    return Scaffold(
      backgroundColor: ColorManagement.primary,
      appBar: AppBars().screensAppBar(""),
      body: ListView.builder(
        itemCount: refundPolicies.length,
        itemBuilder: (context, index) {
          final policy = refundPolicies[index];
          return ExpansionTile(
            iconColor: Colors.orange,
            title: Text(
              policy["title"]!,
              style: const TextStyle(color: Colors.orange),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  policy["content"]!,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
