
import 'dart:convert';

Data dataFromJson(String str) => Data.fromJson(json.decode(str));

String dataToJson(Data data) => json.encode(data.toJson());

class Data {
    final List<Slide> slides;
    final List<Category> categories;
    final List<CategorySub> categorySubs;
    final List<Product> products;
    final List<OfferCategory> offerCategories;

    Data({
        required this.slides,
        required this.categories,
        required this.categorySubs,
        required this.products,
        required this.offerCategories,
    });

    factory Data.fromJson(Map<String, dynamic> json) => Data(
        slides: List<Slide>.from(json["slides"].map((x) => Slide.fromJson(x))),
        categories: List<Category>.from(json["categories"].map((x) => Category.fromJson(x))),
        categorySubs: List<CategorySub>.from(json["categorySubs"].map((x) => CategorySub.fromJson(x))),
        products: List<Product>.from(json["products"].map((x) => Product.fromJson(x))),
        offerCategories: List<OfferCategory>.from(json["offerCategories"].map((x) => OfferCategory.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "slides": List<dynamic>.from(slides.map((x) => x.toJson())),
        "categories": List<dynamic>.from(categories.map((x) => x.toJson())),
        "categorySubs": List<dynamic>.from(categorySubs.map((x) => x.toJson())),
        "products": List<dynamic>.from(products.map((x) => x.toJson())),
        "offerCategories": List<dynamic>.from(offerCategories.map((x) => x.toJson())),
    };
}

class Category {
    final String id;
    final String? image;
    final String name;

    Category({
        required this.id,
        required this.image,
        required this.name,
    });

    factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        image: json["image"],
        name: json["name"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "image": image,
        "name": name,
    };
}

class CategorySub {
    final String id;
    final String categoryId;
    final String name;

    CategorySub({
        required this.id,
        required this.categoryId,
        required this.name,
    });

    factory CategorySub.fromJson(Map<String, dynamic> json) => CategorySub(
        id: json["id"],
        categoryId: json["categoryId"],
        name: json["name"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "categoryId": categoryId,
        "name": name,
    };
}

class OfferCategory {
    final String id;
    final bool isAds;
    final String name;
    final List<Offer> offers;

    OfferCategory({
        required this.id,
        required this.isAds,
        required this.name,
        required this.offers,
    });

    factory OfferCategory.fromJson(Map<String, dynamic> json) => OfferCategory(
        id: json["id"],
        isAds: json["isAds"],
        name: json["name"],
        offers: List<Offer>.from(json["offers"].map((x) => Offer.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "isAds": isAds,
        "name": name,
        "offers": List<dynamic>.from(offers.map((x) => x.toJson())),
    };
}

class Offer {
    final String id;
    final String categoryId;
    final String productId;
    final String image;
    final dynamic product;

    Offer({
        required this.id,
        required this.categoryId,
        required this.productId,
        required this.image,
        required this.product,
    });

    factory Offer.fromJson(Map<String, dynamic> json) => Offer(
        id: json["id"],
        categoryId: json["categoryId"],
        productId: json["productId"],
        image: json["image"],
        product: json["product"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "categoryId": categoryId,
        "productId": productId,
        "image": image,
        "product": product,
    };
}

class Product {
    final String id;
    final String subCategoryId;
    final String productId;
    final String priceId;
    final bool hide;
    final String images;
    final String name;
    final String info;
    final int discount;
    final dynamic product;
    final int price;

    Product({
        required this.id,
        required this.subCategoryId,
        required this.productId,
        required this.priceId,
        required this.hide,
        required this.images,
        required this.name,
        required this.info,
        required this.discount,
        required this.product,
        required this.price,
    });

    factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json["id"],
        subCategoryId: json["subCategoryId"],
        productId: json["productId"],
        priceId: json["priceId"],
        hide: json["hide"],
        images: json["images"],
        name: json["name"],
        info: json["info"],
        discount: json["discount"],
        product: json["product"],
        price: json["price"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "subCategoryId": subCategoryId,
        "productId": productId,
        "priceId": priceId,
        "hide": hide,
        "images": images,
        "name": name,
        "info": info,
        "discount": discount,
        "product": product,
        "price": price,
    };
}

class Slide {
    final String id;
    final String image;

    Slide({
        required this.id,
        required this.image,
    });

    factory Slide.fromJson(Map<String, dynamic> json) => Slide(
        id: json["id"],
        image: json["image"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "image": image,
    };
}
