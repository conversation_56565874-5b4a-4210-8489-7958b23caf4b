import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zamin_phone/app/splash_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'app/controllers/controllers/notification.dart';
import 'app/controllers/languages/language_controller.dart';
import 'app/controllers/languages/locale_controller.dart';
import 'app/controllers/services/notifications.dart';

SharedPreferences? prefs;
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  prefs = await SharedPreferences.getInstance();
  await Firebase.initializeApp();
  Get.put(NotificationController());

  NotificationsService notificationsService = NotificationsService();
  notificationsService.requestNotificationPermission();
  notificationsService.getToken();
  notificationsService.listenToNotifications();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    LocaleController localController = Get.put(LocaleController());
    return GetMaterialApp(
      translations: LangUage(),
      locale: localController.initialLang,
      title: 'Flutter Demo',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all<Color>(Colors.white),
          ),
        ),
        // colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const SplashScreen(),
      // builder: (context, child) {
      //   return Directionality(
      //     textDirection: prefs!.getString('lang') == "ar"
      //         ? TextDirection.ltr
      //         : TextDirection.ltr, // Force LTR for all locales
      //     child: child!,
      //   );
      // },
    );
  }
}
