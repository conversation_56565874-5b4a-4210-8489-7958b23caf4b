import 'package:flutter/material.dart';
import '../../../configs/constants.dart';
import '../../../main.dart';
import '../../controllers/services/products.dart';
import '../../models/data.dart';

class ProductWedgit extends StatefulWidget {
  final Product product;
  const ProductWedgit(this.product, {super.key});

  @override
  State<ProductWedgit> createState() => _ProductWedgitState();
}

class _ProductWedgitState extends State<ProductWedgit> {
  late List<String> favoriteProducts;
  @override
  void initState() {
    favoriteProducts = prefs!.getStringList('favoriteProducts') ?? [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: Image.network(
              Constants().imagerUrl + widget.product.images.split('|')[0],
              fit: BoxFit.cover,
              height: 120,
            ),
          ),
        ),
        SizedBox(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  "  ${ProductApi().switchName(widget.product.name)}",
                  style:const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.white),
                ),
              ),
              // Container(
              //   padding: const EdgeInsets.only(left: 8.0, right: 12),
              //   child: Text(
              //     descr,
              //     style: TextStylesManagment.caption,
              //   ),
              // ),
              Padding(
                padding: const EdgeInsets.only(left: 8.0, right: 12, bottom: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('  ${widget.product.price} IQD',
                        style:
                            const TextStyle(fontSize: 14, color: Colors.white)),
                    GestureDetector(
                      onTap: () {
                        toggleFavorite(widget.product.id);
                      },
                      child: Icon(
                        favoriteProducts.contains(widget.product.id)
                            ? Icons.favorite
                            : Icons.favorite_outline,
                        color: favoriteProducts.contains(widget.product.id)
                            ? Colors.red
                            : Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void toggleFavorite(String articleId) async {
    if (favoriteProducts.contains(articleId)) {
      favoriteProducts.remove(articleId);
    } else {
      favoriteProducts.add(articleId);
    }
    await prefs!.setStringList('favoriteProducts', favoriteProducts);
    setState(() {});
  }
}
