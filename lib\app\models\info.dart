// To parse this JSON data, do
//
//     final info = infoFromJson(jsonString);

import 'dart:convert';

Info infoFromJson(String str) => Info.fromJson(json.decode(str));

String infoToJson(Info data) => json.encode(data.toJson());

class Info {
    final String id;
    final String userId;
    final String clientId;
    final int state;
    final int locationLat;
    final int locationLng;
    final String createdAt;
    final int total;
    final List<Item> items;
    final dynamic states;
    final dynamic user;
    final dynamic client;

    Info({
        required this.id,
        required this.userId,
        required this.clientId,
        required this.state,
        required this.locationLat,
        required this.locationLng,
        required this.createdAt,
        required this.total,
        required this.items,
        required this.states,
        required this.user,
        required this.client,
    });

    factory Info.fromJson(Map<String, dynamic> json) => Info(
        id: json["id"],
        userId: json["userId"],
        clientId: json["clientId"],
        state: json["state"],
        locationLat: json["locationLat"],
        locationLng: json["locationLng"],
        createdAt: json["createdAt"],
        total: json["total"],
        items: List<Item>.from(json["items"].map((x) => Item.fromJson(x))),
        states: json["states"],
        user: json["user"],
        client: json["client"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "userId": userId,
        "clientId": clientId,
        "state": state,
        "locationLat": locationLat,
        "locationLng": locationLng,
        "createdAt": createdAt,
        "total": total,
        "items": List<dynamic>.from(items.map((x) => x.toJson())),
        "states": states,
        "user": user,
        "client": client,
    };
}

class Item {
    final String id;
    final String orderId;
    final String productId;
    final String priceId;
    final String viewProductId;
    final int quantity;
    final int price;
    final ViewProduct viewProduct;
    final Product product;
    final dynamic order;
    int? discount;

    Item({
        required this.id,
        required this.orderId,
        required this.productId,
        required this.priceId,
        required this.viewProductId,
        required this.quantity,
        required this.price,
        required this.viewProduct,
        required this.product,
        required this.order,
        this.discount
    });

    factory Item.fromJson(Map<String, dynamic> json) => Item(
        id: json["id"],
        orderId: json["orderId"],
        productId: json["productId"],
        priceId: json["priceId"],
        viewProductId: json["viewProductId"],
        quantity: json["quantity"],
        price: json["price"],
        viewProduct: ViewProduct.fromJson(json["viewProduct"]),
        product: Product.fromJson(json["product"]),
        order: json["order"],
        discount: json["discount"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "orderId": orderId,
        "productId": productId,
        "priceId": priceId,
        "viewProductId": viewProductId,
        "quantity": quantity,
        "price": price,
        "viewProduct": viewProduct.toJson(),
        "product": product.toJson(),
        "order": order,
        "discount": discount,
    };
}

class Product {
    final String id;
    final String propOne;
    final String propTwo;
    final String name;
    final bool gridBased;
    final int cost;
    final dynamic barcodes;
    final dynamic prices;
    final bool isService;
    final int packQuantity;
    final int boxQuantity;
    final dynamic propertyOne;
    final dynamic propertyTwo;
    int? discount;

    Product({
        required this.id,
        required this.propOne,
        required this.propTwo,
        required this.name,
        required this.gridBased,
        required this.cost,
        required this.barcodes,
        required this.prices,
        required this.isService,
        required this.packQuantity,
        required this.boxQuantity,
        required this.propertyOne,
        required this.propertyTwo,
        this.discount
    });

    factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json["id"],
        propOne: json["propOne"],
        propTwo: json["propTwo"],
        name: json["name"],
        gridBased: json["gridBased"],
        cost: json["cost"],
        barcodes: json["barcodes"],
        prices: json["prices"],
        isService: json["isService"],
        packQuantity: json["packQuantity"],
        boxQuantity: json["boxQuantity"],
        propertyOne: json["propertyOne"],
        propertyTwo: json["propertyTwo"],
        discount: json["discount"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "propOne": propOne,
        "propTwo": propTwo,
        "name": name,
        "gridBased": gridBased,
        "cost": cost,
        "barcodes": barcodes,
        "prices": prices,
        "isService": isService,
        "packQuantity": packQuantity,
        "boxQuantity": boxQuantity,
        "propertyOne": propertyOne,
        "propertyTwo": propertyTwo,
        "discount": discount,
    };
}

class ViewProduct {
    final String id;
    final String subCategoryId;
    final String productId;
    final String priceId;
    final String images;
    final String name;
    final String info;
    final dynamic product;
    final int price;

    ViewProduct({
        required this.id,
        required this.subCategoryId,
        required this.productId,
        required this.priceId,
        required this.images,
        required this.name,
        required this.info,
        required this.product,
        required this.price,
    });

    factory ViewProduct.fromJson(Map<String, dynamic> json) => ViewProduct(
        id: json["id"],
        subCategoryId: json["subCategoryId"],
        productId: json["productId"],
        priceId: json["priceId"],
        images: json["images"],
        name: json["name"],
        info: json["info"],
        product: json["product"],
        price: json["price"],
    );

    Map<String, dynamic> toJson() => {
        "id": id,
        "subCategoryId": subCategoryId,
        "productId": productId,
        "priceId": priceId,
        "images": images,
        "name": name,
        "info": info,
        "product": product,
        "price": price,
    };
}
