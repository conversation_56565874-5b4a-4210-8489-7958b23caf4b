import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/models/data.dart';
import 'package:zamin_phone/app/views/screens/product_screen.dart';
import 'package:zamin_phone/app/views/widgets/product_wedgit.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/configs/text_style.dart';
import '../../controllers/controllers/products_controller.dart';
import '../../controllers/services/products.dart' show ProductApi;
import '../widgets/app_bar.dart';

class ProductsScreen extends StatefulWidget {
  final String categorySubsId;
  final Map categoryName;
  const ProductsScreen(this.categorySubsId, this.categoryName, {super.key});

  @override
  State<ProductsScreen> createState() => _ProductsScreenState();
}

class _ProductsScreenState extends State<ProductsScreen> {
  late final List<Product> products;
  late List<Product> filteredProducts;
  final TextEditingController searchController = TextEditingController();
  ProductsController productsController = Get.find();
  RangeValues _priceRange = const RangeValues(0.0, 10000000.0);

  @override
  void initState() {
    super.initState();
    products =
        productsController.getProductsBucategorySubId(widget.categorySubsId);
    filteredProducts = products;

    // Set initial price range based on actual product prices
    if (products.isEmpty) {
      const minPrice = 0.0;
      const maxPrice = 0.0;
      _priceRange = const RangeValues(minPrice, maxPrice);
    } else {
      final minPrice = products
          .reduce((min, product) => min.price < product.price ? min : product)
          .price
          .toDouble();
      final maxPrice = products
          .reduce((max, product) => max.price > product.price ? max : product)
          .price
          .toDouble();
      _priceRange = RangeValues(minPrice, maxPrice);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      appBar: AppBars().screensAppBar(
        ProductApi().switchName(widget.categoryName['name']),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: TextField(
                      controller: searchController,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        fillColor: Colors.orange.withValues(alpha: 0.5),
                        filled: true,
                        labelText: 'Search',
                        labelStyle: const TextStyle(color: Colors.white),
                        prefixIcon:
                            const Icon(Icons.search, color: Colors.white),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none, // Hides the border line
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none, // Hides the border line
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none, // Hides the border line
                        ),
                      ),
                      onChanged: (value) {
                        _searchedProducts(); // Filters products as text is entered
                      },
                    ),
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Container(
                  height: 50,
                  width: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    color: Colors.orange.withValues(alpha: 0.5),
                  ),
                  child: IconButton(
                    icon: Icon(Icons.filter_alt,
                        color: ColorManagement.secondoryColor),
                    onPressed: _showPriceFilterBottomSheet,
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Container(
                  height: 50,
                  width: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    color: Colors.white,
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.filter_alt_off,
                      color: ColorManagement.secondoryColor,
                    ),
                    onPressed: () => _resetProducts(),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Expanded(
              // Wrap GridView in Expanded to avoid layout issues
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    childAspectRatio: 0.94,
                    crossAxisCount: 2,
                    mainAxisSpacing: 20,
                    crossAxisSpacing: 15),
                shrinkWrap: true,
                physics: const ScrollPhysics(),
                itemCount: filteredProducts.length,
                itemBuilder: (context, index) {
                  final product = filteredProducts[index];
                  return Container(
                    decoration: BoxDecoration(
                        // border: Border.all(width: 1, color: Colors.amber[200]!),
                        boxShadow: [
                          BoxShadow(
                              spreadRadius: 3,
                              blurRadius: 2,
                              color: Colors.grey.withValues(alpha: 0.2))
                        ],
                        border: Border.all(
                            width: 1, color: ColorManagement.secondoryColor),
                        color: ColorManagement.primary,
                        borderRadius: BorderRadius.circular(15)),
                    child: GestureDetector(
                      onTap: () => Get.to(SingleProductScreen(
                        product,
                        products,
                        widget.categoryName,
                      )),
                      child: ProductWedgit(product),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  //======================================
  void _searchedProducts() {
    setState(() {
      // Filter only by name
      filteredProducts = products
          .where((product) => product.name
              .toLowerCase()
              .contains(searchController.text.toLowerCase()))
          .toList();
    });
  }

  void _filterProducts() {
    setState(() {
      filteredProducts = products
          .where((product) =>
              product.price >= _priceRange.start &&
              product.price <= _priceRange.end)
          .toList();
    });
  }

  void _showPriceFilterBottomSheet() {
    showModalBottomSheet<void>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // StatefulBuilder's setState
            return Container(
              decoration: const BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  )),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text('Price Range'.tr),
                  RangeSlider(
                    activeColor: Colors.white,
                    values: _priceRange,
                    min: products
                        .reduce((min, product) =>
                            min.price < product.price ? min : product)
                        .price
                        .toDouble(),
                    max: products
                        .reduce((max, product) =>
                            max.price > product.price ? max : product)
                        .price
                        .toDouble(),
                    divisions: 5,
                    labels: RangeLabels(_priceRange.start.toStringAsFixed(2),
                        _priceRange.end.toStringAsFixed(2)),
                    onChanged: (RangeValues newRange) {
                      // Update the RangeSlider's labels and range in the bottom sheet
                      setState(() => _priceRange = newRange);
                    },
                  ),
                  TextButton(
                    onPressed: () {
                      // Apply the filter in the main widget's setState
                      _filterProducts();
                      Navigator.pop(context); // Close the bottom sheet
                    },
                    child: Text(
                      'Apply Filter'.tr,
                      style: TextStylesManagment.medTitle,
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _resetProducts() {
    setState(() {
      filteredProducts = products;
      searchController.clear();
    });
  }
}
