import 'package:flutter/material.dart';
import 'package:zamin_phone/configs/colors.dart';

class PasswordFieldController extends ValueNotifier<bool> {
  PasswordFieldController() : super(true);

  bool get isObscured => value;

  void toggleVisibility() {
    value = !value;
  }
}

ValueListenableBuilder textField(controller, text, keyboard,
    {String? errorText, PasswordFieldController? passwordController}) {
  final bool isPasswordField = text == 'Password' || text == 'New Password';
  final IconData? icon;

  switch (text) {
    case 'Username':
      icon = Icons.person;
      break;
    case 'Phone':
      icon = Icons.phone;
      break;
    case 'Password':
    case 'New Password':
      icon = Icons.lock;
      break;
    default:
      icon = null;
  }

  return ValueListenableBuilder<bool>(
    valueListenable: passwordController ?? ValueNotifier(false),
    builder: (context, isObscured, child) {
      return TextFormField(
        style: const TextStyle(color: Colors.white),
        controller: controller,
        keyboardType: keyboard,
        obscureText:
            isPasswordField && passwordController != null ? isObscured : false,
        decoration: InputDecoration(
            suffixIcon: isPasswordField && passwordController != null
                ? Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(
                          isObscured ? Icons.visibility_off : Icons.visibility,
                          color: Colors.white,
                        ),
                        onPressed: () => passwordController.toggleVisibility(),
                      ),
                      const Icon(
                        Icons.lock,
                        color: Colors.white,
                      ),
                    ],
                  )
                : icon != null
                    ? Icon(
                        icon,
                        color: Colors.white,
                      )
                    : null,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(
                  color: errorText != null ? Colors.red : Colors.grey[200]!,
                  width: 1.0),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: BorderSide(
                  color: errorText != null ? Colors.red : Colors.orange,
                  width: 2.0),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: const BorderSide(color: Colors.red, width: 1.0),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10.0),
              borderSide: const BorderSide(color: Colors.red, width: 2.0),
            ),
            filled: true,
            fillColor: ColorManagement.primary,
            hintText: text,
            hintStyle: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
            errorText: errorText,
            errorStyle: const TextStyle(color: Colors.red, fontSize: 12)),
      );
    },
  );
}
