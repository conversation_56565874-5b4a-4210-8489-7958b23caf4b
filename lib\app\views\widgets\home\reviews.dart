import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/models/data.dart';
import 'package:zamin_phone/app/views/widgets/cached_image.dart';
import '../../../../configs/colors.dart';
import '../../../../configs/constants.dart';
import '../../../controllers/services/products.dart';
import '../../screens/product_screen.dart';

class ReviewsWidget extends StatelessWidget {
  final List<Product> products;
  final List<Category> categories;
  final List<CategorySub> categorySubs;

  const ReviewsWidget(this.products, this.categories, this.categorySubs,
      {super.key});

  @override
  Widget build(BuildContext context) {
    List<Icon> icons = [
      Icon(Icons.star, color: Colors.orange[600], size: 15),
      Icon(Icons.star, color: Colors.orange[600], size: 15),
      Icon(Icons.star, color: Colors.orange[600], size: 15),
      Icon(Icons.star, color: Colors.orange[600], size: 15),
      Icon(Icons.star, color: Colors.orange[600], size: 15),
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: products.map<Widget>((product) {
          return Container(
              width: 220,
              height: 125,
              margin: const EdgeInsets.symmetric(horizontal: 5),
              child: Stack(
                children: [
                  Positioned(
                      top: 0,
                      left: 0,
                      child: Container(
                        width: 200,
                        height: 125,
                        padding: const EdgeInsets.only(left: 10),
                        decoration: BoxDecoration(
                            border: Border.all(
                                width: 1,
                                color: Colors.orange.withValues(alpha: 0.3)),
                            color: ColorManagement.primary,
                            borderRadius: BorderRadius.circular(25)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              "  ${ProductApi().switchName(product.name)}",
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Colors.white),
                            ),
                            Container(
                              padding:
                                  const EdgeInsets.only(left: 8.0, right: 12),
                              child: Row(
                                children: [
                                  const Text(
                                    "5.0  ",
                                    style: TextStyle(color: Colors.white),
                                  ),
                                  Row(
                                    children: icons,
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              width: 135,
                              padding:
                                  const EdgeInsets.only(left: 8.0, right: 12),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text('${product.price} \$',
                                      style: const TextStyle(
                                          fontSize: 14, color: Colors.white)),
                                  const Icon(
                                    Icons.favorite_border_outlined,
                                    size: 20,
                                    color: Colors.white,
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      )),
                  Positioned(
                      top: 25,
                      left: 140,
                      child: GestureDetector(
                        onTap: () {
                          final subCat = categorySubs.firstWhere(
                              (item) => item.id == product.subCategoryId);

                          final catID = categories
                              .firstWhere((item) => item.id == subCat.id);

                          final prodData = {
                            "name": subCat.name,
                            "cat": catID.name
                          };
                          Get.to(
                              SingleProductScreen(product, products, prodData));
                        },
                        child: SizedBox(
                          width: 75,
                          height: 75,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(15),
                            child: Image(
                                image: CachedImage(
                                        imageLink: Constants().imagerUrl +
                                            product.images.split('|')[0])
                                    .cachedImage(),
                                fit: BoxFit.cover),
                          ),
                        ),
                      )),
                ],
              ));
        }).toList(),
      ),
    );
  }
}
