buildscript {
    ext.kotlin_version = '1.9.23' // Updated to align with AGP 8.4.1 and Flutter recommendations
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.4.1' // Updated AGP version
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        // START: FlutterFire Configuration
        classpath 'com.google.gms:google-services:4.4.2' // Check for latest version
        // END: FlutterFire Configuration
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
