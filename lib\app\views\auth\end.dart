import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/splash_screen.dart';

import '../../../configs/colors.dart';
import '../../../configs/text_style.dart';

class EndAuthScreen extends StatelessWidget {
  const EndAuthScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Icon(
              Icons.check_circle,
              size: 150,
              color: ColorManagement.primary,
            ),
            const Text(
              'Congratulation ',
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 30,
                  fontWeight: FontWeight.bold),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 50.0),
              child: Text(
                "You'r account has been successfully Created",
                style: TextStylesManagment.middle,
              ),
            ),
            SizedBox(
              height: Get.height * 0.30,
            ),
            SizedBox(
              width: Get.width - 40,
              child: ElevatedButton(
                  onPressed: () {
                    Get.offAll(() => const SplashScreen());
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: Text(
                      "Continue Shopping",
                      style: TextStylesManagment.button,
                    ),
                  )),
            ),
            const SizedBox(
              height: 50,
            ),
          ],
        ),
      ),
    );
  }
}
