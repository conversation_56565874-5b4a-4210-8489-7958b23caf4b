{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Flutter\\zamin_phone\\android\\app\\.cxx\\RelWithDebInfo\\5h6r2p16\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Flutter\\zamin_phone\\android\\app\\.cxx\\RelWithDebInfo\\5h6r2p16\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}