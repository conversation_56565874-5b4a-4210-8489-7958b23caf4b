// To parse this JSON data, do
//
//     final history = historyFromJson(jsonString);

import 'dart:convert';

List<History> historyFromJson(String str) =>
    List<History>.from(json.decode(str).map((x) => History.fromJson(x)));

String historyToJson(List<History> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class History {
  final String id;
  int state;
  final String createdAt;
  final int total;
  final dynamic items;

  History({
    required this.id,
    required this.state,
    required this.createdAt,
    required this.total,
    required this.items,
  });

  factory History.fromJson(Map<String, dynamic> json) => History(
        id: json["id"],
        state: json["state"],
        createdAt: json["createdAt"],
        total: json["total"],
        items: json["items"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "state": state,
        "createdAt": createdAt,
        "total": total,
        "items": items,
      };
}
