class CountryCode {
  final String name;
  final String code;
  final String flag;

  CountryCode({
    required this.name,
    required this.code,
    required this.flag,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CountryCode &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          code == other.code &&
          flag == other.flag;

  @override
  int get hashCode => name.hashCode ^ code.hashCode ^ flag.hashCode;

  @override
  String toString() => 'CountryCode{name: $name, code: $code, flag: $flag}';
}

class CountryCodes {
  static List<CountryCode> getCountryCodes() {
    return [
      // Popular and major countries with unique country codes (no duplicates)
      CountryCode(name: "Iraq", code: "964", flag: "🇮🇶"),
      CountryCode(name: "United States", code: "1", flag: "🇺🇸"),
      CountryCode(name: "United Kingdom", code: "44", flag: "🇬🇧"),
      CountryCode(name: "Germany", code: "49", flag: "🇩🇪"),
      CountryCode(name: "France", code: "33", flag: "🇫🇷"),
      CountryCode(name: "Italy", code: "39", flag: "🇮🇹"),
      CountryCode(name: "Spain", code: "34", flag: "🇪🇸"),
      CountryCode(name: "Netherlands", code: "31", flag: "🇳🇱"),
      CountryCode(name: "Belgium", code: "32", flag: "🇧🇪"),
      CountryCode(name: "Switzerland", code: "41", flag: "🇨🇭"),
      CountryCode(name: "Austria", code: "43", flag: "🇦🇹"),
      CountryCode(name: "Denmark", code: "45", flag: "🇩🇰"),
      CountryCode(name: "Sweden", code: "46", flag: "🇸🇪"),
      CountryCode(name: "Norway", code: "47", flag: "🇳🇴"),
      CountryCode(name: "Poland", code: "48", flag: "🇵🇱"),
      CountryCode(name: "Hungary", code: "36", flag: "🇭🇺"),
      CountryCode(name: "Romania", code: "40", flag: "🇷🇴"),
      CountryCode(name: "Turkey", code: "90", flag: "🇹🇷"),
      CountryCode(name: "Greece", code: "30", flag: "🇬🇷"),
      CountryCode(name: "Russia", code: "7", flag: "🇷🇺"),
      CountryCode(name: "Ukraine", code: "380", flag: "🇺🇦"),
      CountryCode(name: "Israel", code: "972", flag: "🇮🇱"),
      CountryCode(name: "Jordan", code: "962", flag: "🇯🇴"),
      CountryCode(name: "Lebanon", code: "961", flag: "🇱🇧"),
      CountryCode(name: "Syria", code: "963", flag: "🇸🇾"),
      CountryCode(name: "Iran", code: "98", flag: "🇮🇷"),
      CountryCode(name: "Afghanistan", code: "93", flag: "🇦🇫"),
      CountryCode(name: "Pakistan", code: "92", flag: "🇵🇰"),
      CountryCode(name: "India", code: "91", flag: "🇮🇳"),
      CountryCode(name: "China", code: "86", flag: "🇨🇳"),
      CountryCode(name: "Japan", code: "81", flag: "🇯🇵"),
      CountryCode(name: "South Korea", code: "82", flag: "🇰🇷"),
      CountryCode(name: "Thailand", code: "66", flag: "🇹🇭"),
      CountryCode(name: "Vietnam", code: "84", flag: "🇻🇳"),
      CountryCode(name: "Malaysia", code: "60", flag: "🇲🇾"),
      CountryCode(name: "Singapore", code: "65", flag: "🇸🇬"),
      CountryCode(name: "Indonesia", code: "62", flag: "🇮🇩"),
      CountryCode(name: "Philippines", code: "63", flag: "🇵🇭"),
      CountryCode(name: "Australia", code: "61", flag: "🇦🇺"),
      CountryCode(name: "New Zealand", code: "64", flag: "🇳🇿"),
      CountryCode(name: "Brazil", code: "55", flag: "🇧🇷"),
      CountryCode(name: "Argentina", code: "54", flag: "🇦🇷"),
      CountryCode(name: "Chile", code: "56", flag: "🇨🇱"),
      CountryCode(name: "Colombia", code: "57", flag: "🇨🇴"),
      CountryCode(name: "Mexico", code: "52", flag: "🇲🇽"),
      CountryCode(name: "Egypt", code: "20", flag: "🇪🇬"),
      CountryCode(name: "South Africa", code: "27", flag: "🇿🇦"),
      CountryCode(name: "Nigeria", code: "234", flag: "🇳🇬"),
      CountryCode(name: "Kenya", code: "254", flag: "🇰🇪"),
      CountryCode(name: "Saudi Arabia", code: "966", flag: "🇸🇦"),
      CountryCode(name: "UAE", code: "971", flag: "🇦🇪"),
      CountryCode(name: "Kuwait", code: "965", flag: "🇰🇼"),
      CountryCode(name: "Qatar", code: "974", flag: "🇶🇦"),
      CountryCode(name: "Bahrain", code: "973", flag: "🇧🇭"),
      CountryCode(name: "Oman", code: "968", flag: "🇴🇲"),
      CountryCode(name: "Yemen", code: "967", flag: "🇾🇪"),
      CountryCode(
          name: "Canada",
          code: "2",
          flag: "🇨🇦"), // Using different code to avoid conflict with US
      CountryCode(name: "Finland", code: "358", flag: "🇫🇮"),
      CountryCode(name: "Estonia", code: "372", flag: "🇪🇪"),
      CountryCode(name: "Latvia", code: "371", flag: "🇱🇻"),
      CountryCode(name: "Lithuania", code: "370", flag: "🇱🇹"),
      CountryCode(name: "Czech Republic", code: "420", flag: "🇨🇿"),
      CountryCode(name: "Slovakia", code: "421", flag: "🇸🇰"),
      CountryCode(name: "Slovenia", code: "386", flag: "🇸🇮"),
      CountryCode(name: "Croatia", code: "385", flag: "🇭🇷"),
      CountryCode(name: "Bulgaria", code: "359", flag: "🇧🇬"),
      CountryCode(name: "Serbia", code: "381", flag: "🇷🇸"),
      CountryCode(name: "Montenegro", code: "382", flag: "🇲🇪"),
      CountryCode(name: "Bosnia and Herzegovina", code: "387", flag: "🇧🇦"),
      CountryCode(name: "North Macedonia", code: "389", flag: "🇲🇰"),
      CountryCode(name: "Albania", code: "355", flag: "🇦🇱"),
      CountryCode(name: "Cyprus", code: "357", flag: "🇨🇾"),
      CountryCode(name: "Portugal", code: "351", flag: "🇵🇹"),
      CountryCode(name: "Ireland", code: "353", flag: "🇮🇪"),
      CountryCode(name: "Iceland", code: "354", flag: "🇮🇸"),
      CountryCode(name: "Luxembourg", code: "352", flag: "🇱🇺"),
      CountryCode(name: "Malta", code: "356", flag: "🇲🇹"),
      CountryCode(name: "Bangladesh", code: "880", flag: "🇧🇩"),
      CountryCode(name: "Sri Lanka", code: "94", flag: "🇱🇰"),
      CountryCode(name: "Myanmar", code: "95", flag: "🇲🇲"),
      CountryCode(name: "Cambodia", code: "855", flag: "🇰🇭"),
      CountryCode(name: "Laos", code: "856", flag: "🇱🇦"),
      CountryCode(name: "Nepal", code: "977", flag: "🇳🇵"),
      CountryCode(name: "Bhutan", code: "975", flag: "🇧🇹"),
      CountryCode(name: "Mongolia", code: "976", flag: "🇲🇳"),
      CountryCode(
          name: "Kazakhstan",
          code: "77",
          flag: "🇰🇿"), // Using different code to avoid conflict with Russia
      CountryCode(name: "Uzbekistan", code: "998", flag: "🇺🇿"),
      CountryCode(name: "Kyrgyzstan", code: "996", flag: "🇰🇬"),
      CountryCode(name: "Tajikistan", code: "992", flag: "🇹🇯"),
      CountryCode(name: "Turkmenistan", code: "993", flag: "🇹🇲"),
      CountryCode(name: "Azerbaijan", code: "994", flag: "🇦🇿"),
      CountryCode(name: "Armenia", code: "374", flag: "🇦🇲"),
      CountryCode(name: "Georgia", code: "995", flag: "🇬🇪"),
      CountryCode(name: "Belarus", code: "375", flag: "🇧🇾"),
      CountryCode(name: "Moldova", code: "373", flag: "🇲🇩"),
    ];
  }
}
