{"buildFiles": ["D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Flutter\\zamin_phone\\android\\app\\.cxx\\Debug\\616w5343\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\Flutter\\zamin_phone\\android\\app\\.cxx\\Debug\\616w5343\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}