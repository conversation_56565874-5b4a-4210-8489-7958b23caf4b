import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class CachedImage {
  final String imageLink;

  CachedImage({required this.imageLink});
  cachedImage() {
    return CachedNetworkImageProvider(
      imageLink,
      cacheManager: CustomCacheManager(),
    );
  }
}

class CustomCacheManager extends CacheManager {
  static const key = 'customCacheKey';

  CustomCacheManager()
      : super(Config(
          key,
          maxNrOfCacheObjects: 150, // Maximum number of cached files
          stalePeriod:
              const Duration(days: 365), // How long to keep files cached
        ));
}
