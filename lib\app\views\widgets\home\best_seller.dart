import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/views/widgets/cached_image.dart';
import '../../../../configs/colors.dart';
import '../../../../configs/constants.dart';
import '../../../../configs/text_style.dart';
import '../../../../main.dart';
import '../../../controllers/services/products.dart';
import '../../../models/data.dart';
import '../../screens/product_screen.dart';

class BestSellerWidget extends StatefulWidget {
  final List<Offer> offers;
  final List<Product> products;
  final List<Category> categories;
  final List<CategorySub> categorySubs;
  final String name;

  const BestSellerWidget(
      this.offers, this.products, this.categories, this.categorySubs, this.name,
      {super.key});

  @override
  State<BestSellerWidget> createState() => _BestSellerWidgetState();
}

class _BestSellerWidgetState extends State<BestSellerWidget> {
  late List<String> favoriteProducts;
  @override
  void initState() {
    favoriteProducts = prefs!.getStringList('favoriteProducts') ?? [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: Get.width,
          child: Text(
            ProductApi().switchName(widget.name),
            style: TextStylesManagment.primarySmall,
          ),
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: widget.offers.map<Widget>((offer) {
              final product = widget.products
                  .firstWhere((prod) => prod.id == offer.productId);
              return Container(
                  width: 140,
                  height: 200,
                  margin: const EdgeInsets.symmetric(horizontal: 5),
                  child: Stack(
                    children: [
                      Positioned(
                          top: 0,
                          left: 0,
                          child: Container(
                            width: 140,
                            height: 200,
                            padding: const EdgeInsets.only(top: 120),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    width: 1,
                                    color:
                                        Colors.orange.withValues(alpha: 0.3)),
                                color: ColorManagement.primary,
                                borderRadius: BorderRadius.circular(25)),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  "  ${ProductApi().switchName(product.name)}",
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                      color: Colors.white),
                                ),
                                Container(
                                    padding: const EdgeInsets.only(
                                        left: 8.0, right: 12),
                                    child: FutureBuilder(
                                        future: ProductApi()
                                            .getProductRate(product.id),
                                        builder:
                                            (context, AsyncSnapshot snapshot) {
                                          if (snapshot.hasData &&
                                              !snapshot.hasError) {
                                            final rating =
                                                snapshot.data.toString();
                                            return Row(
                                              children: [
                                                Text(
                                                  "${snapshot.data}  ",
                                                  style: const TextStyle(
                                                      color: Colors.white),
                                                ),
                                                RatingBarIndicator(
                                                  unratedColor: Colors.white38,
                                                  itemSize: 10,
                                                  itemCount: 5,
                                                  rating: double.parse(rating),
                                                  itemBuilder:
                                                      (context, index) {
                                                    return const Icon(
                                                        Icons.star,
                                                        color: Colors.yellow);
                                                  },
                                                )
                                              ],
                                            );
                                          } else {
                                            return const Text('',
                                                style: TextStyle(
                                                    color: Colors.white));
                                          }
                                        })),
                                Container(
                                  width: 135,
                                  padding: const EdgeInsets.only(
                                      left: 8.0, right: 12),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text('${product.price} IQD',
                                          style: const TextStyle(
                                              fontSize: 14,
                                              color: Colors.white)),
                                      GestureDetector(
                                        onTap: () {
                                          toggleFavorite(product.id);
                                        },
                                        child: Icon(
                                          favoriteProducts.contains(product.id)
                                              ? Icons.favorite
                                              : Icons.favorite_outline,
                                          color: favoriteProducts
                                                  .contains(product.id)
                                              ? Colors.red
                                              : Colors.white,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )),
                      Positioned(
                          top: 20,
                          left: 20,
                          child: GestureDetector(
                            onTap: () {
                              final subCat = widget.categorySubs.firstWhere(
                                  (item) => item.id == product.subCategoryId);

                              final catID = widget.categories.firstWhere(
                                  (item) => item.id == subCat.categoryId);

                              final prodData = {
                                "name": subCat.name,
                                "cat": catID.name
                              };
                              Get.to(SingleProductScreen(
                                  product, widget.products, prodData));
                            },
                            child: SizedBox(
                              width: 100,
                              height: 100,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(15),
                                child: Image(
                                    image: CachedImage(
                                            imageLink: Constants().imagerUrl +
                                                (offer.image != ""
                                                    ? offer.image
                                                    : product.images
                                                        .split('|')[0]))
                                        .cachedImage(),
                                    fit: BoxFit.cover),
                              ),
                            ),
                          )),
                    ],
                  ));
            }).toList(),
          ),
        ),
      ],
    );
  }

  void toggleFavorite(String articleId) async {
    if (favoriteProducts.contains(articleId)) {
      favoriteProducts.remove(articleId);
    } else {
      favoriteProducts.add(articleId);
    }
    await prefs!.setStringList('favoriteProducts', favoriteProducts);
    setState(() {});
  }
}
