import 'package:flutter/material.dart';
import 'package:zamin_phone/app/views/screens/profile/docs/faq.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';

import '../../../../configs/colors.dart';
import '../../../../main.dart';

class FAQPage extends StatelessWidget {
  const FAQPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = prefs!.getString('lang');
    List<Map<String, String>> faqData;

    switch (lang) {
      case "ar":
        faqData = Faq().ar;
        break;
      case "en":
        faqData = Faq().en;
        break;
      case "so":
        faqData = Faq().so;
        break;
      default:
        faqData = Faq().ku;
    }

    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      appBar: AppBars().screensAppBar('FAQs'),
      body: ListView.builder(
        itemCount: faqData.length,
        itemBuilder: (context, index) {
          return ExpansionTile(
            title: Text(
              faqData[index]['question']!,
              style: const TextStyle(
                  fontWeight: FontWeight.bold, color: Colors.orange),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  faqData[index]['answer']!,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
