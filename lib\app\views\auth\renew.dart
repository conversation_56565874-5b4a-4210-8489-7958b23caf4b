import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:zamin_phone/app/controllers/services/auth.dart';
import 'package:zamin_phone/app/views/auth/register.dart';
import 'package:zamin_phone/app/views/widgets/textfield.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/configs/text_style.dart';

class RenewSecreen extends StatefulWidget {
  final String phoneNumber;
  const RenewSecreen(this.phoneNumber, {super.key});

  @override
  State<RenewSecreen> createState() => _RenewSecreenState();
}

class _RenewSecreenState extends State<RenewSecreen> {
  bool hasError = false;
  String currentText = "";
  final formKey = GlobalKey<FormState>();
  late final String phoneNumber;
  TextEditingController textEditingController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  PasswordFieldController passwordFieldController = PasswordFieldController();

  // Validation error messages
  String? usernameError;
  String? passwordError;

  DateTime? _lastResendTime;
  static const Duration _resendCooldown = Duration(minutes: 5);
  Timer? _timer;
  Duration _remaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    phoneNumber = widget.phoneNumber;
    _lastResendTime = DateTime.now(); // Start cooldown on screen load
    _startTimer();
  }

  @override
  void dispose() {
    passwordFieldController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _updateRemaining();
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateRemaining();
    });
  }

  void _updateRemaining() {
    setState(() {
      if (_lastResendTime == null) {
        _remaining = Duration.zero;
      } else {
        final diff = DateTime.now().difference(_lastResendTime!);
        _remaining = _resendCooldown - diff;
        if (_remaining.isNegative) _remaining = Duration.zero;
      }
    });
  }

  bool get canResend => _remaining == Duration.zero;

  void _handleResendOtp() async {
    if (!canResend) {
      final min = _remaining.inMinutes;
      final sec = _remaining.inSeconds % 60;
      Get.snackbar(
        'Wait',
        'Please wait ${min > 0 ? "$min min " : ""}${sec}s before resending OTP.',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }
    textEditingController.clear();
    await AuthApi().getOtpForPassword(phoneNumber);
    Get.snackbar('Info', "OTP code resent",
        backgroundColor: Colors.blue,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM);
    setState(() {
      currentText = "";
      _lastResendTime = DateTime.now();
    });
    _startTimer();
  }

  // Validation functions
  String? validateUsername(String value) {
    if (value.trim().isEmpty) {
      return 'Username is required';
    }
    return null;
  }

  String? validatePassword(String value) {
    if (value.trim().isEmpty) {
      return 'New password is required';
    }
    return null;
  }

  bool validateForm() {
    bool isValid = true;

    setState(() {
      passwordError = validatePassword(newPasswordController.text);

      if (passwordError != null) isValid = false;
      if (currentText.length != 6) isValid = false;
    });

    return isValid;
  }

  @override
  Widget build(BuildContext context) {
    StreamController<ErrorAnimationType>? errorController;
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 80),
              const Text(
                "Reset Password",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 30,
                    fontWeight: FontWeight.bold),
              ),
              Text(
                "Please enter the 6 digit code and new password",
                style: TextStylesManagment.primarySmall,
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 30),

              // OTP Input
              const Text(
                "Verification Code",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold),
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: PinCodeTextField(
                  appContext: context,
                  length: 6,
                  obscureText: false,
                  animationType: AnimationType.fade,
                  pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(5),
                      fieldHeight: 50,
                      fieldWidth: 40,
                      activeColor: Colors.grey[200],
                      activeFillColor: ColorManagement.lightPrimary,
                      disabledColor: Colors.grey[300],
                      inactiveColor: Colors.grey[300],
                      selectedColor: Colors.grey[300],
                      errorBorderColor: Colors.grey[300],
                      inactiveFillColor: Colors.grey[300],
                      selectedFillColor: Colors.grey[300]),
                  animationDuration: const Duration(milliseconds: 300),
                  enableActiveFill: true,
                  keyboardType: TextInputType.number,
                  errorAnimationController: errorController,
                  controller: textEditingController,
                  onCompleted: (v) {},
                  onChanged: (value) {
                    setState(() {
                      currentText = value;
                    });
                  },
                  beforeTextPaste: (text) {
                    return true;
                  },
                ),
              ), // New Password Field
              Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: textField(
                    newPasswordController, "New Password", TextInputType.text,
                    errorText: passwordError,
                    passwordController: passwordFieldController),
              ),

              const SizedBox(height: 30),

              // Renew Password Button
              SizedBox(
                width: Get.width - 120,
                child: ElevatedButton(
                    onPressed: () async {
                      if (!validateForm()) {
                        if (currentText.length != 6) {
                          Get.snackbar(
                              'Error', "Please enter the 6 digit OTP code",
                              backgroundColor: Colors.red,
                              colorText: Colors.white,
                              snackPosition: SnackPosition.BOTTOM);
                        }
                        return;
                      }

                      final request = {
                        "Phone": phoneNumber,
                        "Password": newPasswordController.text,
                        "Otp": currentText,
                      };
                      final result = await AuthApi().forgetpassword(request);
                      if (!result['status']) {
                        Get.snackbar('Error', result['error'],
                            backgroundColor: Colors.red,
                            colorText: Colors.white,
                            snackPosition: SnackPosition.BOTTOM);
                      } else {
                        Get.snackbar('Success', "Password reset successfully",
                            backgroundColor: Colors.green,
                            colorText: Colors.white,
                            snackPosition: SnackPosition.BOTTOM);
                        Get.offAll(() => const AuthScreen());
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(15.0),
                      child: Text(
                        "Renew Password",
                        style: TextStylesManagment.button,
                      ),
                    )),
              ),
              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    "Did not receive code? ",
                    style: TextStylesManagment.caption,
                  ),
                  InkWell(
                    onTap: _handleResendOtp,
                    child: Text(
                      'Resend',
                      style: TextStylesManagment.primaryBody,
                    ),
                  )
                ],
              ),
              if (!canResend)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'You can resend OTP in  ${_remaining.inMinutes.toString().padLeft(2, '0')}:${(_remaining.inSeconds % 60).toString().padLeft(2, '0')}',
                    style: const TextStyle(color: Colors.orange, fontSize: 14),
                  ),
                ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
