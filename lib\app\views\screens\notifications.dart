import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:zamin_phone/configs/text_style.dart';

import '../../../configs/colors.dart';
import '../widgets/app_bar.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({super.key});

  Future<List<Map<String, dynamic>>> getNotifications() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String>? notifications = prefs.getStringList('notifications') ?? [];
    return notifications
        .map((e) => Map<String, dynamic>.from(jsonDecode(e)))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManagement.primary,
      appBar: AppBars().screensAppBar("Notifications"),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: getNotifications(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return const Center(child: Text('Error loading notifications'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No notifications'));
          } else {
            return ListView.builder(
              itemCount: snapshot.data!.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                  child: Card(
                    color: ColorManagement.secondoryColor,
                    child: ListTile(
                      leading:
                          const Image(image: AssetImage('./assets/logo.png')),
                      title: Text(
                        snapshot.data![index]['title'] ?? 'No title',
                        style: TextStylesManagment.medTitle,
                      ),
                      subtitle: Text(
                        snapshot.data![index]['body'] ?? 'No body',
                        style: TextStylesManagment.caption,
                      ),
                    ),
                  ),
                );
              },
            );
          }
        },
      ),
    );
  }
}
