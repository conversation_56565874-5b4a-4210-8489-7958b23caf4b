import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

Future<void> checkForceUpdate(BuildContext context) async {
  final remoteConfig = FirebaseRemoteConfig.instance;

  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout:const Duration(seconds: 10),
    minimumFetchInterval: Duration.zero, // Always fetch fresh for demo/dev
  ));

  await remoteConfig.fetchAndActivate();

  final forceUpdate = remoteConfig.getBool('force_update');
  final latestVersion = Platform.isAndroid
      ? remoteConfig.getString('latest_android_version')
      : remoteConfig.getString('latest_iOS_version');
  final updateUrl = Platform.isAndroid
      ? remoteConfig.getString('update_url_android')
      : remoteConfig.getString('update_url_ios');

  final packageInfo = await PackageInfo.fromPlatform();
  final currentVersion = packageInfo.version;

  if (_isVersionOutdated(currentVersion, latestVersion)) {
    _showUpdateDialog(context, forceUpdate, updateUrl);
  }
}

bool _isVersionOutdated(String current, String latest) {
  final currentParts = current.split('.').map(int.parse).toList();
  final latestParts = latest.split('.').map(int.parse).toList();

  for (int i = 0; i < latestParts.length; i++) {
    if (i >= currentParts.length || currentParts[i] < latestParts[i]) return true;
    if (currentParts[i] > latestParts[i]) return false;
  }
  return false;
}

void _showUpdateDialog(BuildContext context, bool force, String url) {
  showDialog(
    barrierDismissible: !force, // Prevent closing if force is true
    context: context,
    builder: (ctx) => AlertDialog(
      title:const Text('Update Required'),
      content:const Text('A new version of the app is available. Please update to continue.'),
      actions: [
        if (!force)
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child:const Text('Later'),
          ),
        TextButton(
          onPressed: () {
            launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
          },
          child:const Text('Update Now'),
        ),
      ],
    ),
  );
}
