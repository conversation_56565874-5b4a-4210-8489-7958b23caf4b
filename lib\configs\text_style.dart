import 'package:flutter/material.dart';
import 'package:zamin_phone/configs/colors.dart';

class TextStylesManagment {
  static const _fontFamily = 'Rabar';

  static const TextStyle title = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 24,
    fontWeight: FontWeight.bold,
  );
  static const TextStyle largTitle = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 32,
    fontWeight: FontWeight.bold,
  );
  static const TextStyle medTitle = TextStyle(
      fontFamily: _fontFamily,
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: Colors.white);

  static TextStyle productTitle = const TextStyle(
    color: Colors.white,
    fontFamily: _fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.bold,
  );

  static TextStyle primaryLarg = TextStyle(
      fontFamily: _fontFamily,
      fontSize: 24,
      fontWeight: FontWeight.bold,
      color: ColorManagement.primary);

  static TextStyle primaryMed = const TextStyle(
      fontFamily: _fontFamily,
      fontSize: 20,
      fontWeight: FontWeight.bold,
      color: Colors.orange);
  static TextStyle primarySmall = const TextStyle(
      fontFamily: _fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: Colors.white);
  static TextStyle primaryExtraLarg = const TextStyle(
      fontFamily: _fontFamily,
      fontSize: 32,
      fontWeight: FontWeight.bold,
      color: Colors.orange);

  static const TextStyle subtitle = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 18,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle body = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16,
  );
  static const TextStyle extraBody = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 32,
  );
  static TextStyle categories = const TextStyle(
      fontFamily: _fontFamily, fontSize: 12, color: Colors.orange);

  static TextStyle primaryBody = const TextStyle(
      fontFamily: _fontFamily, fontSize: 16, color: Colors.orange);

  static TextStyle button = TextStyle(
      fontFamily: _fontFamily,
      fontSize: 20,
      color: ColorManagement.secondoryColor);

  static const TextStyle caption = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    color: Colors.grey,
  );
  static const TextStyle darkCaption = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12,
    //color: Colors.grey,
  );
  static const TextStyle middle = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14,
    color: Colors.white,
  );
}
