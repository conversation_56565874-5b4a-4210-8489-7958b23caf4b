import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/views/widgets/cached_image.dart';

import '../../../../configs/colors.dart';
import '../../../../configs/constants.dart';
import '../../../../configs/text_style.dart';
import '../../../../main.dart';
import '../../../controllers/services/products.dart';
import '../../../models/data.dart';
import '../../screens/product_screen.dart';

class DailyOffers extends StatefulWidget {
  final List<Offer> offers;
  final List<Product> products;
  final List<Category> categories;
  final List<CategorySub> categorySubs;
  final String name;

  const DailyOffers(
      this.offers, this.products, this.categories, this.categorySubs, this.name,
      {super.key});

  @override
  State<DailyOffers> createState() => _DailyOffersState();
}

class _DailyOffersState extends State<DailyOffers> {
  late List<String> favoriteProducts;
  @override
  void initState() {
    favoriteProducts = prefs!.getStringList('favoriteProducts') ?? [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: Get.width,
          child: Text(
            ProductApi().switchName(widget.name),
            style: TextStylesManagment.primarySmall,
          ),
        ),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: widget.offers.map<Widget>((offer) {
              final product = widget.products
                  .firstWhere((prod) => prod.id == offer.productId);

              String baseDescription = ProductApi().switchName(product.info);
              String desc;

              if (baseDescription.length <= 1) {
                // If baseDescription is empty or has only 1 character
                desc = baseDescription.isEmpty ? "" : "$baseDescription...";
              } else {
                // baseDescription has at least 2 characters.
                // We want a snippet from the second character (index 1), max 29 chars long.
                int snippetDesiredEndIndex = 1 +
                    29; // End index for a 29-char snippet starting from index 1
                int actualEndIndex =
                    snippetDesiredEndIndex > baseDescription.length
                        ? baseDescription.length
                        : snippetDesiredEndIndex;
                desc = "${baseDescription.substring(1, actualEndIndex)}...";
              }

              return Container(
                  width: 230,
                  height: 125,
                  margin: const EdgeInsets.symmetric(horizontal: 5),
                  child: Stack(
                    children: [
                      Positioned(
                          top: 0,
                          left: 0,
                          child: Container(
                            width: 230,
                            height: 125,
                            padding: const EdgeInsets.only(left: 70, top: 10),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    width: 1,
                                    color:
                                        Colors.orange.withValues(alpha: 0.3)),
                                color: ColorManagement.primary,
                                borderRadius: BorderRadius.circular(25)),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Center(
                                  child: Text(
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    "  ${ProductApi().switchName(product.name)}",
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                        color: Colors.white),
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.only(
                                      left: 25.0, right: 5),
                                  child: Text(
                                    desc,
                                    style: TextStylesManagment.caption,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(right: 12),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text('${product.price} IQD',
                                          style: const TextStyle(
                                              fontSize: 14,
                                              color: Colors.white)),
                                      GestureDetector(
                                        onTap: () {
                                          toggleFavorite(product.id);
                                        },
                                        child: Icon(
                                          favoriteProducts.contains(product.id)
                                              ? Icons.favorite
                                              : Icons.favorite_outline,
                                          color: favoriteProducts
                                                  .contains(product.id)
                                              ? Colors.red
                                              : Colors.white,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )),
                      Positioned(
                          top: 25,
                          left: 10,
                          child: GestureDetector(
                            onTap: () {
                              final subCat = widget.categorySubs.firstWhere(
                                  (item) => item.id == product.subCategoryId);

                              final catID = widget.categories.firstWhere(
                                  (item) => item.id == subCat.categoryId);

                              final prodData = {
                                "name": subCat.name,
                                "cat": catID.name
                              };
                              Get.to(SingleProductScreen(
                                  product, widget.products, prodData));
                            },
                            child: SizedBox(
                              width: 75,
                              height: 75,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(15),
                                child: Image(
                                  image: CachedImage(
                                          imageLink: Constants().imagerUrl +
                                              (offer.image != ""
                                                  ? offer.image
                                                  : product.images
                                                      .split('|')[0]))
                                      .cachedImage(),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          )),
                    ],
                  ));
            }).toList(),
          ),
        ),
      ],
    );
  }

  void toggleFavorite(String articleId) async {
    if (favoriteProducts.contains(articleId)) {
      favoriteProducts.remove(articleId);
    } else {
      favoriteProducts.add(articleId);
    }
    await prefs!.setStringList('favoriteProducts', favoriteProducts);
    setState(() {});
  }
}
