import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/app/views/screens/order_detail.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';
import 'package:zamin_phone/configs/colors.dart';

import '../../models/history.dart';

class UserOrders extends StatefulWidget {
  const UserOrders({super.key});

  @override
  State<UserOrders> createState() => _UserOrdersState();
}

class _UserOrdersState extends State<UserOrders> {
  List<History> orders = [];
  int page = 0;
  bool isLoading = false;
  bool hasMore = true;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController()..addListener(_scrollListener);
    _fetchOrders();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _fetchOrders() async {
    if (isLoading || !hasMore) return;

    setState(() {
      isLoading = true;
    });

    try {
      List<History> newOrders = await ProductApi().getHistory(page);
      setState(() {
        if (newOrders.isEmpty) {
          hasMore = false;
        } else {
          orders.addAll(newOrders);
          page++;
        }
      });
    } catch (e) {
      Get.snackbar("Error", "Failed to load orders");
    }

    setState(() {
      isLoading = false;
    });
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent &&
        !isLoading) {
      _fetchOrders();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      appBar: AppBars().screensAppBar("My Orders"),
      body: orders.isEmpty && !isLoading
          ? const Center(
              child: Text(
              "No orders found.",
              style: const TextStyle(color: Colors.white),
            ))
          : ListView.builder(
              controller: _scrollController,
              itemCount: orders.length + (hasMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= orders.length) {
                  return const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(),
                  );
                }
                final order = orders[index];
                final formattedDate = DateFormat('yyyy-MM-dd').format(
                  DateTime.parse(order.createdAt),
                );

                return Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: BoxDecoration(
                      border: Border.all(
                          color: ColorManagement.lightPrimary, width: 0.2),
                      boxShadow: [
                        BoxShadow(
                            spreadRadius: 3,
                            blurRadius: 2,
                            color: Colors.grey.withValues(alpha: 0.2))
                      ],
                      color: ColorManagement.primary,
                      borderRadius: BorderRadius.circular(10)),
                  child: ListTile(
                    leading: CircleAvatar(
                      radius: 15,
                      backgroundColor: ColorManagement.secondoryColor,
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(
                      "Order ID: ${order.id}",
                      style: const TextStyle(color: Colors.white),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("Date: $formattedDate",
                            style: const TextStyle(color: Colors.white60)),
                        Text("Total: ${order.total}",
                            style: const TextStyle(color: Colors.white)),
                        Text(
                          "Status: ${capitalizeFirstLetter(status[order.state])}",
                          style: TextStyle(
                              color: getStatusColor(order.state),
                              fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: () => Get.to(() => OrderDetail(order.id)),
                          icon: const Icon(Icons.visibility,
                              color: Colors.orange),
                        ),
                        IconButton(
                          onPressed: () {
                            confirmCancle(context, order.id, order.state);
                          },
                          icon: const Icon(Icons.remove, color: Colors.red),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }

  confirmCancle(context, id, state) {
    return showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
              title: const Text("Confirm Cancel Order"),
              content:
                  const Text("Are you sure you want to cancel this order?"),
              actions: [
                TextButton(
                  onPressed: () async {
                    if (state == 0) {
                      final request = {"id": id, "state": 3};
                      try {
                        // Cancel order
                        await ProductApi().cancleOrder(request);
                        // Find the index of the order in the list
                        int index =
                            orders.indexWhere((order) => order.id == id);
                        if (index != -1) {
                          // Update the status of the specific order
                          setState(() {
                            orders[index].state =
                                3; // Set the state to canceled
                          });
                        }
                      } catch (e) {
                        Get.snackbar("Error", "Failed to cancel order",
                            snackPosition: SnackPosition.BOTTOM);
                      }
                      Navigator.of(context).pop();
                    } else {
                      Get.snackbar("Error",
                          "You don't have permission to cancel this order",
                          snackPosition: SnackPosition.BOTTOM);
                      Navigator.of(context).pop();
                    }
                  },
                  child: Text('Yes'.tr),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text('No'.tr),
                )
              ]);
        });
  }
}

List<String> status = [
  "Ordered",
  "Delivering",
  "Delivered",
  "UserCanceled",
  "ControlCanceled",
  "Representative",
  "Accepted"
];

String capitalizeFirstLetter(String input) {
  if (input.isEmpty) return input;
  return input[0].toUpperCase() + input.substring(1);
}

Color getStatusColor(int state) {
  switch (state) {
    case 0:
      return Colors.blue; // Ordered
    case 1:
      return Colors.orange; // Delivering
    case 2:
      return Colors.green; // Delivered
    case 3:
      return Colors.red; // UserCanceled
    case 4:
      return Colors.grey; // ControlCanceled
    case 5:
      return Colors.indigo; // Representative
    default:
      return Colors.green[800]!; // Accepted;
  }
}
