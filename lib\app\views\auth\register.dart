import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/controllers/products_controller.dart';
import 'package:zamin_phone/app/controllers/services/auth.dart';
import 'package:zamin_phone/app/views/auth/check_google_sign.dart';
import 'package:zamin_phone/app/views/auth/renew.dart';
import 'package:zamin_phone/app/views/auth/verify.dart';
import 'package:zamin_phone/app/views/main_tab.dart';
import 'package:zamin_phone/app/views/widgets/textfield.dart';
import 'package:zamin_phone/app/models/country_codes.dart';
import 'package:zamin_phone/configs/constants.dart';
import 'package:zamin_phone/configs/text_style.dart';
import 'package:zamin_phone/main.dart';
import '../../../configs/colors.dart';
import '../../controllers/services/auth_service.dart';
import '../../controllers/services/notifications.dart';

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  TextEditingController nameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  PasswordFieldController passwordFieldController = PasswordFieldController();
  late bool haveAccount;
  CountryCode selectedCountryCode =
      CountryCodes.getCountryCodes().first; // Default to Iraq (first in list)

  // Validation error messages
  String? nameError;
  String? phoneError;
  String? passwordError;

  // final TextEditingController _usernameController = TextEditingController();
  bool _isLoading = false;
  final _authService = AuthService();

  Future<void> _handleGoogleLogin(lat, long) async {
    setState(() => _isLoading = true);
    NotificationsService notificationsService = NotificationsService();
    notificationsService.requestNotificationPermission();
    notificationsService.getToken();
    notificationsService.listenToNotifications();
    Map<String, dynamic> backendResponse =
        await _authService.signInWithGoogleAndSendToBackend(
            //username: _usernameController.text.trim(),
            fcmToken:
                prefs!.getString("fcm_token"), // Replace with real FCM token
            apiUrl: "${Constants().baseAuthUrl}OAuth", // Replace with real URL
            lat: lat ?? 0.0,
            long: long ?? 0.0);
    if (backendResponse['phone'] == "") {
      Get.to(CheckGoogleSign(backendResponse['token'],
          'Complete Google Sign-In', backendResponse['token']));
    } else {
      prefs!.setString("token", backendResponse['token']);
      prefs!.setString("phone", backendResponse['phone']);
      prefs!.setString("username", backendResponse['name']);
      prefs!.setString("id", backendResponse['id']);
      prefs!.setInt("deliveryCost", backendResponse['deliverCost']);
      prefs!.setBool("isLogined", true);
      prefs!.setBool("isGuest", false);
      Get.snackbar('Great!', "Login Succeed",
          backgroundColor: Colors.green,
          colorText: Colors.black,
          snackPosition: SnackPosition.BOTTOM);
      Get.to(const MainTab());
    }

    setState(() => _isLoading = false);
  }

  Future<void> _handleAppleLogin(lat, long) async {
    Map<String, dynamic> backendResponse =
        await _authService.signInWithAppleAndSendToBackend(
            //username: _usernameController.text.trim(),
            fcmToken:
                prefs!.getString("fcm_token"), // Replace with real FCM token
            apiUrl: "${Constants().baseAuthUrl}OAuth", // Replace with real URL
            lat: lat,
            long: long);

    setState(() => _isLoading = false);
  }

  @override
  void initState() {
    haveAccount = true;
    super.initState();
  }

  @override
  void dispose() {
    passwordFieldController.dispose();
    super.dispose();
  }

  // Validation functions
  String? validateName(String value) {
    if (value.trim().isEmpty) {
      return 'Username is required';
    }
    return null;
  }

  String? validatePhone(String value) {
    if (value.trim().isEmpty) {
      return 'Phone number is required';
    }
    // Basic phone number validation (digits only, minimum length)
    // if (value.replaceAll(RegExp(r'[^\d]'), '').length < 10) {
    //   return 'Please enter a valid phone number';
    // }
    return null;
  }

  String? validatePassword(String value) {
    if (value.trim().isEmpty) {
      return 'Password is required';
    }

    return null;
  }

  bool validateForm() {
    bool isValid = true;

    setState(() {
      phoneError = validatePhone(phoneController.text);
      passwordError = validatePassword(passwordController.text);

      if (!haveAccount) {
        nameError = validateName(nameController.text);
        if (nameError != null) isValid = false;
      } else {
        nameError = null;
      }

      if (phoneError != null) isValid = false;
      if (passwordError != null) isValid = false;
    });

    return isValid;
  }

  // Helper function to extract main error message from API response
  String getMainErrorMessage(Map<String, dynamic> errorResponse) {
    try {
      // Case 1: Simple error with title (User Already Exist)
      if (errorResponse.containsKey('title') &&
          errorResponse['title'] != 'One or more validation errors occurred.') {
        return errorResponse['title'];
      }

      // Case 2: Validation errors with errors object
      if (errorResponse.containsKey('errors')) {
        final errors = errorResponse['errors'] as Map<String, dynamic>;

        // Get the first error message from any field
        for (var fieldErrors in errors.values) {
          if (fieldErrors is List && fieldErrors.isNotEmpty) {
            final errorMessage = fieldErrors.first.toString();
            return errorMessage;
          }
        }
      }

      // Case 3: If title exists but is generic, try to get detail
      if (errorResponse.containsKey('detail')) {
        return errorResponse['detail'];
      }

      // Case 4: Fallback to title if available
      if (errorResponse.containsKey('title')) {
        return errorResponse['title'];
      }

      // Case 5: Default fallback
      return 'An error occurred. Please try again.';
    } catch (e) {
      return 'An error occurred. Please try again.';
    }
  }

  @override
  Widget build(BuildContext context) {
    final lat = prefs!.getDouble('Latitude');
    final long = prefs!.getDouble('Longitude');

    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      resizeToAvoidBottomInset: true,
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        !haveAccount ? 'Create your' : "Welcome to Zamin",
                        style: TextStylesManagment.primaryExtraLarg,
                      ),
                      if (!haveAccount)
                        Text(
                          'Account',
                          style: TextStylesManagment.primaryExtraLarg,
                        ),
                      const SizedBox(
                        height: 20,
                      ),
                      phoneNumberMethod(),
                      if (!haveAccount)
                        Container(
                            width: 300,
                            margin: const EdgeInsets.symmetric(vertical: 20),
                            //  padding: const EdgeInsets.symmetric(horizontal: 30),
                            child: textField(
                                nameController, "Username", TextInputType.text,
                                errorText: nameError)),
                      Container(
                        width: 300,
                        margin: const EdgeInsets.symmetric(vertical: 20),
                        // padding: const EdgeInsets.symmetric(horizontal: 30),
                        child: textField(
                            passwordController, "Password", TextInputType.text,
                            errorText: passwordError,
                            passwordController: passwordFieldController),
                      ),
                      if (haveAccount)
                        GestureDetector(
                            onTap: () async {
                              if (phoneController.text == "") {
                                Get.snackbar("error", "Fill Phone Number",
                                    backgroundColor: Colors.red,
                                    colorText: Colors.white,
                                    snackPosition: SnackPosition.BOTTOM);
                              } else {
                                final formattedPhone = AuthApi()
                                    .reformatPhoneNumber(phoneController.text,
                                        1, selectedCountryCode.code);
                                await AuthApi()
                                    .getOtpForPassword(formattedPhone);

                                Get.to(RenewSecreen(formattedPhone));
                              }
                            },
                            child: Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 60),
                                width: Get.width,
                                child: Text(
                                  'Forgot password'.tr,
                                  textAlign: TextAlign.end,
                                  style: TextStylesManagment.categories,
                                ))),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: 300,
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(
                                      10.0), // Adjust radius as needed
                                ),
                                backgroundColor: Colors.white10),
                            onPressed: () async {
                              if (!validateForm()) return;

                              final fmcToken = prefs!.getString("fcm_token");
                              final Map<String, dynamic> request;
                              final phoneNumber = AuthApi().reformatPhoneNumber(
                                  phoneController.text,
                                  1,
                                  selectedCountryCode.code);

                              if (haveAccount) {
                                request = {
                                  "userName": phoneNumber,
                                  "password": passwordController.text,
                                  "fmcToken": fmcToken.toString(),
                                  "fmc_token": fmcToken.toString(),
                                };
                                final res = await AuthApi().login(request);
                                if (res.containsKey("error") &&
                                    res["error"] == true) {
                                  final errorMessage =
                                      getMainErrorMessage(res['data']);
                                  Get.snackbar('Error', errorMessage,
                                      backgroundColor: Colors.red,
                                      colorText: Colors.white,
                                      snackPosition: SnackPosition.BOTTOM);
                                } else {
                                  prefs!.setString("token", res['token']);
                                  prefs!.setString("phone", res['phone']);
                                  prefs!.setString("username", res['name']);
                                  prefs!.setString("id", res['id']);
                                  prefs!.setBool("isLogined", true);
                                  prefs!.setBool("isGuest", false);
                                  prefs!.setInt(
                                      "deliveryCost", res['deliverCost']);
                                  Get.snackbar('Great!', "Login Succeed",
                                      backgroundColor: Colors.green,
                                      colorText: Colors.black,
                                      snackPosition: SnackPosition.BOTTOM);
                                  ProductsController productsController =
                                      Get.find();
                                  await productsController.getAllProducts();

                                  Get.offAll(() => const MainTab());
                                }
                              } else {
                                request = {
                                  "userName": nameController.text,
                                  "phone": phoneNumber,
                                  "password": passwordController.text,
                                  "longitude": long,
                                  "latitude": lat,
                                  "fmcToken": fmcToken.toString(),
                                  "fmc_token": fmcToken.toString(),
                                };
                                final res = await AuthApi().signUp(request);
                                if (kDebugMode) {
                                  print(res);
                                }

                                if (res['code'] != 200) {
                                  final errorData = jsonDecode(res['data']);
                                  final errorMessage =
                                      getMainErrorMessage(errorData);
                                  Get.snackbar('Error', errorMessage,
                                      snackPosition: SnackPosition.BOTTOM,
                                      backgroundColor: Colors.red,
                                      colorText: Colors.white);
                                } else {
                                  Get.to(() => VerifyScreen(res['data']));
                                }
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(15.0),
                              child: Text(
                                !haveAccount ? 'Sign up' : 'Login',
                                style: TextStylesManagment.productTitle,
                              ),
                            )),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Text(
                        'or',
                        style: TextStylesManagment.primaryMed,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      if (_isLoading)
                        const CircularProgressIndicator()
                      else if (Platform.isAndroid)
                        GestureDetector(
                          onTap: () {
                            _handleGoogleLogin(lat, long);
                          },
                          child: Container(
                            width: 300,
                            height: 50,
                            decoration: BoxDecoration(
                                color: Colors.white10,
                                borderRadius: BorderRadius.circular(10)),
                            child: Row(
                              children: [
                                const SizedBox(
                                  width: 10,
                                ),
                                FaIcon(
                                  FontAwesomeIcons.google,
                                  color: Colors.orange[300],
                                ),
                                const SizedBox(
                                  width: 30,
                                ),
                                Text(
                                  "Continue with Google",
                                  style: TextStylesManagment.productTitle,
                                )
                              ],
                            ),
                          ),
                        )
                      else if (Platform.isIOS)
                        GestureDetector(
                          onTap: () {
                            _handleAppleLogin(lat, long);
                          },
                          child: Container(
                            width: 300,
                            height: 50,
                            decoration: BoxDecoration(
                                color: Colors.white10,
                                borderRadius: BorderRadius.circular(10)),
                            child: Row(
                              children: [
                                const SizedBox(
                                  width: 10,
                                ),
                                FaIcon(
                                  FontAwesomeIcons.apple,
                                  color: Colors.orange[300],
                                ),
                                const SizedBox(
                                  width: 30,
                                ),
                                Text(
                                  "Continue with Apple ID",
                                  style: TextStylesManagment.productTitle,
                                )
                              ],
                            ),
                          ),
                        ),
                      const SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            !haveAccount
                                ? "Already have account? Please "
                                : "Don't have account? Please ",
                            style: TextStylesManagment.caption,
                          ),
                          InkWell(
                            onTap: () {
                              setState(() {
                                haveAccount = !haveAccount;
                              });
                            },
                            child: Text(
                              !haveAccount ? 'Login' : 'Sign up',
                              style: TextStylesManagment.primaryBody,
                            ),
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      GestureDetector(
                        onTap: () async {
                          ProductsController productsController =
                              Get.put(ProductsController());
                          await productsController.getAllProducts();

                          prefs!.setBool("isLogined", false);
                          Get.off(() => const MainTab());
                        },
                        child: const Text(
                          'Login as guest',
                          style: TextStylesManagment.caption,
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Container phoneNumberMethod() {
    return Container(
      width: 300,
      margin: const EdgeInsets.symmetric(vertical: 20),
      padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 3),
      decoration: BoxDecoration(
          color: ColorManagement.primary,
          border: Border.all(color: Colors.white, width: 1),
          borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          // Country Code Dropdown
          DropdownButtonHideUnderline(
            child: DropdownButton<CountryCode>(
              value: selectedCountryCode,
              isDense: true,
              dropdownColor: ColorManagement.secondoryColor,
              icon: const Icon(Icons.keyboard_arrow_down,
                  color: Colors.white, size: 16),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              items: CountryCodes.getCountryCodes().map((CountryCode country) {
                return DropdownMenuItem<CountryCode>(
                  value: country,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(country.flag, style: const TextStyle(fontSize: 16)),
                      const SizedBox(width: 8),
                      Text('+${country.code}',
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (CountryCode? newValue) {
                if (newValue != null) {
                  setState(() {
                    selectedCountryCode = newValue;
                  });
                  if (kDebugMode) {
                    print(
                        "Selected country: ${newValue.name} (+${newValue.code})");
                  }
                }
              },
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
              child: TextField(
            controller: phoneController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
                hintStyle:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                suffixIcon: Icon(
                  Icons.phone,
                  color: Colors.white,
                ),
                hintText: ""),
          )

              // textField(phoneController, "Phone",
              //     TextInputType.number,
              //     errorText: phoneError)
              ),
        ],
      ),
    );
  }
}
