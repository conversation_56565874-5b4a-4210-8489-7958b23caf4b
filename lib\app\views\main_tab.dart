import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Import for SystemNavigator
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/controllers/main_controller.dart';
import 'package:zamin_phone/app/views/tabs/cart.dart';
import 'package:zamin_phone/app/views/tabs/categories.dart';
import 'package:zamin_phone/app/views/tabs/favorite.dart';
import 'package:zamin_phone/app/views/tabs/home.dart';
import 'package:zamin_phone/app/views/tabs/profile.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';
import 'package:curved_labeled_navigation_bar/curved_navigation_bar.dart';
import 'package:curved_labeled_navigation_bar/curved_navigation_bar_item.dart';
import '../../configs/colors.dart';

class MainTab extends StatelessWidget {
  const MainTab({super.key});

  final Color _activeButtonColor = Colors.white;
  final Color _inactiveButtonColor = const Color(0xff222223);
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<MainController>();
    final pages = [
      const HomeScreen(),
      const CategoriesScreen(),
      const CartScreen(),
      const FavoriteScreen(),
      const ProfileScreen(),
    ];
    final titles = [
      "home".tr,
      "Categories".tr,
      "My Cart".tr,
      "My Favorites".tr,
      "profile".tr,
    ];
    return PopScope(
      canPop: false, // Prevents immediate exit
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        bool exitApp = await _showExitConfirmation(context);
        if (exitApp) {
          // ignore: use_build_context_synchronously
          SystemNavigator.pop(); // Changed to SystemNavigator.pop()
        }
      },
      child: Obx(() {
        return SafeArea(
          child: Scaffold(
            backgroundColor: ColorManagement.secondoryColor,
            appBar: AppBars()
                .customAppBar(titles[controller.navigationIndex.value]),
            body: pages[controller.navigationIndex.value],
            bottomNavigationBar: CurvedNavigationBar(
              buttonBackgroundColor: Colors.orange,
              height: 50,
              color: Colors.black,
              backgroundColor: ColorManagement.secondoryColor,
              items: [
                CurvedNavigationBarItem(
                  child: Icon(
                    Icons.home_outlined,
                    color: controller.navigationIndex.value == 0
                        ? _inactiveButtonColor
                        : _activeButtonColor,
                  ),
                ),
                CurvedNavigationBarItem(
                  child: Icon(Icons.grid_view_outlined,
                      color: controller.navigationIndex.value == 1
                          ? _inactiveButtonColor
                          : _activeButtonColor),
                ),
                CurvedNavigationBarItem(
                  child: Icon(Icons.shopping_cart_outlined,
                      color: controller.navigationIndex.value == 2
                          ? _inactiveButtonColor
                          : _activeButtonColor),
                ),
                CurvedNavigationBarItem(
                  child: Icon(Icons.favorite_border_outlined,
                      color: controller.navigationIndex.value == 3
                          ? _inactiveButtonColor
                          : _activeButtonColor),
                ),
                CurvedNavigationBarItem(
                  child: Icon(Icons.person_2_outlined,
                      color: controller.navigationIndex.value == 4
                          ? _inactiveButtonColor
                          : _activeButtonColor),
                ),
              ],
              onTap: (index) {
                controller.changeCurrentIndex(index);
              },
            ),
          ),
        );
      }),
    );
  }

  Future<bool> _showExitConfirmation(BuildContext context) async {
    return await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text("Exit App".tr),
            content: Text("Are you sure you want to exit?".tr),
            actions: [
              TextButton(
                onPressed: () =>
                    Navigator.of(context).pop(false), // Stay in app
                child: Text("No".tr),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true), // Exit app
                child: Text("Yes".tr),
              ),
            ],
          ),
        ) ??
        false; // Default to false if the dialog is dismissed
  }
}
