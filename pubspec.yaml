name: zamin_phone
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.15+16

environment:
  sdk: ^3.5.1

dependencies:
  flutter:
    sdk: flutter


  cupertino_icons: ^1.0.8
  get: ^4.6.6
  http: ^1.2.2
  shared_preferences: ^2.5.3
  geolocator: ^10.0.0
  geocoding: ^2.0.0
  pin_code_fields: ^8.0.1
  video_player: ^2.9.5
  carousel_slider: ^5.0.0
  lottie: ^3.1.2
  sqflite: ^2.4.0
  path: ^1.9.0
  cached_network_image: ^3.4.1
  barcode_widget: ^2.0.4
  intl: ^0.20.1
  curved_labeled_navigation_bar: ^2.0.5
  url_launcher: ^6.3.1
  firebase_core: ^3.8.1
  firebase_messaging: ^15.1.6
  flutter_rating_bar: ^4.0.1
  flutter_cache_manager: ^3.4.1
  google_sign_in: 6.2.1
  firebase_auth: ^5.6.1
  sign_in_with_apple: ^7.0.1
  font_awesome_flutter: ^10.8.0
  firebase_remote_config: ^5.4.7
  package_info_plus: ^8.3.0
  

  

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
#   flutter_launcher_icons: "^0.14.2"

# flutter_launcher_icons:
#   android: "launcher_icon"
#   ios: true
#   image_path: "assets/icon/icon.png"
#   remove_alpha_ios: true
  #min_sdk_android: 21
  #adaptive_icon_background: "#ffffff" # You can use a color or image for the background
  #adaptive_icon_foreground: "assets/icon/icon.png" # For adaptive icons on Android

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/logo.png
    - assets/loading1.mp4
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Rabar
      fonts:
        - asset: fonts/Rabar.ttf
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
