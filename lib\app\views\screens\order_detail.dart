import 'package:flutter/material.dart';
import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';
import 'package:zamin_phone/configs/constants.dart';

import '../../../configs/colors.dart';

class OrderDetail extends StatelessWidget {
  final String orderId;
  const OrderDetail(this.orderId, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManagement.primary,
      appBar: AppBars().screensAppBar("Order Details"),
      body: FutureBuilder(
          future: ProductApi().getOrderById(orderId),
          builder: (context, AsyncSnapshot snapshot) {
            if (snapshot.hasData) {
              final data = snapshot.data!.items;
              return ListView.builder(
                  itemCount: data.length,
                  itemBuilder: (context, index) {
                    final item = data[index];
                    final disc =
                        item.discount ?? 0 / (item.discount ?? 0 + item.price);
                    return Container(
                        height: 125,
                        margin: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 5),
                        decoration: BoxDecoration(
                            border: Border.all(
                                color: ColorManagement.lightPrimary,
                                width: 0.2),
                            boxShadow: [
                              BoxShadow(
                                  spreadRadius: 3,
                                  blurRadius: 2,
                                  color: Colors.grey.withValues(alpha: 0.2))
                            ],
                            color: ColorManagement.secondoryColor,
                            borderRadius: BorderRadius.circular(10)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(left: 8),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        Constants().imagerUrl +
                                            item.viewProduct.images
                                                .split('|')[0],
                                        width: 50,
                                        height: 50,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Text(
                                    ProductApi()
                                        .switchName(item.viewProduct.name),
                                    textAlign: TextAlign.start,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: Colors.white),
                                    overflow: TextOverflow.ellipsis,
                                    softWrap: true,
                                  ),

                                  // ),
                                ]),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Leading image

                                const SizedBox(width: 10),

                                // Title and Quantity
                                Expanded(
                                  child: Row(
                                    children: [
                                      Text(
                                        "Qty.: ${item.quantity}",
                                        style: const TextStyle(
                                            color: Colors.white),
                                      ),
                                      const Text(
                                        "",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ),
                                Text(
                                  " Price: ${item.price} IQD   ",
                                  style: const TextStyle(color: Colors.white),
                                ),

                                // Trailing remove button
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text("   Discount  : $disc %",
                                    style:
                                        const TextStyle(color: Colors.white)),
                                Text(
                                    " Total  Price: ${item.price * item.quantity} IQD ",
                                    style:
                                        const TextStyle(color: Colors.white)),
                              ],
                            )
                          ],
                        ));
                  });
            } else {
              return const Center(child: CircularProgressIndicator());
            }
          }),
    );
  }
}
