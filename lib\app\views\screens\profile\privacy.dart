import 'package:flutter/material.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';
import 'package:zamin_phone/configs/colors.dart';

import '../../../../main.dart';
import 'docs/privacy.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = prefs!.getString('lang');
    List<Map<String, String>> privacyList;

    switch (lang) {
      case "ar":
        privacyList = Privacy().ar;
        break;
      case "en":
        privacyList = Privacy().en;
        break;
      case "so":
        privacyList = Privacy().so;
        break;
      default:
        privacyList = Privacy().ku;
    }
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      appBar: AppBars().screensAppBar(""),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView.builder(
          itemCount: privacyList.length,
          itemBuilder: (context, index) {
            final section = privacyList[index];
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildSectionTitle(section['title']!),
                buildSectionContent(section['content']!),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Text(
        title,
        style: const TextStyle(
            fontWeight: FontWeight.bold, fontSize: 18, color: Colors.orange),
      ),
    );
  }

  Widget buildSectionContent(String content) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Text(content,
          textAlign: TextAlign.justify,
          style: const TextStyle(fontSize: 16, color: Colors.white)),
    );
  }
}
