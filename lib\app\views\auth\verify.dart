import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:zamin_phone/app/controllers/services/auth.dart';
import 'package:zamin_phone/app/views/auth/end.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/configs/text_style.dart';
import 'package:zamin_phone/main.dart';

class VerifyScreen extends StatefulWidget {
  final String id;
  const VerifyScreen(this.id, {super.key});

  @override
  State<VerifyScreen> createState() => _VerifyScreenState();
}

class _VerifyScreenState extends State<VerifyScreen> {
  bool hasError = false;
  String currentText = "";
  final formKey = GlobalKey<FormState>();
  late final String id;
  TextEditingController textEditingController = TextEditingController();

  DateTime? _lastResendTime;
  static const Duration _resendCooldown = Duration(minutes: 5);
  Timer? _timer;
  Duration _remaining = Duration.zero;

  @override
  void initState() {
    super.initState();
    id = widget.id;
    _lastResendTime = DateTime.now(); // Start cooldown on screen load
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _updateRemaining();
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateRemaining();
    });
  }

  void _updateRemaining() {
    setState(() {
      if (_lastResendTime == null) {
        _remaining = Duration.zero;
      } else {
        final diff = DateTime.now().difference(_lastResendTime!);
        _remaining = _resendCooldown - diff;
        if (_remaining.isNegative) _remaining = Duration.zero;
      }
    });
  }

  bool get canResend => _remaining == Duration.zero;

  void _handleResendOtp() async {
    if (!canResend) {
      final min = _remaining.inMinutes;
      final sec = _remaining.inSeconds % 60;
      Get.snackbar(
        'Wait',
        'Please wait ${min > 0 ? "$min min " : ""}${sec}s before resending OTP.',
        backgroundColor: Colors.orange,
        colorText: Colors.white,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }
    textEditingController.clear();
    final res = await AuthApi().resendOtp(id);
    setState(() {
      currentText = res;
      _lastResendTime = DateTime.now();
    });
    _startTimer();
  }

  @override
  Widget build(BuildContext context) {
    StreamController<ErrorAnimationType>? errorController;
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            "Verification Code",
            style: TextStyle(
                color: Colors.white, fontSize: 30, fontWeight: FontWeight.bold),
          ),
          Text(
            "Please enter the 6 digit code",
            style: TextStylesManagment.primarySmall,
          ),
          Padding(
            padding: const EdgeInsets.all(50.0),
            child: PinCodeTextField(
              appContext: context,
              length: 6,
              obscureText: false,
              animationType: AnimationType.fade,
              pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(5),
                  fieldHeight: 50,
                  fieldWidth: 40,
                  activeColor: Colors.grey[200],
                  activeFillColor: ColorManagement.lightPrimary,
                  disabledColor: Colors.grey[300],
                  inactiveColor: Colors.grey[300],
                  selectedColor: Colors.grey[300],
                  errorBorderColor: Colors.grey[300],
                  inactiveFillColor: Colors.grey[300],
                  selectedFillColor: Colors.grey[300]),
              animationDuration: const Duration(milliseconds: 300),
              //backgroundColor: Colors.blue.shade50,
              enableActiveFill: true,
              keyboardType: TextInputType.number,
              errorAnimationController: errorController,
              controller: textEditingController,
              onCompleted: (v) {},
              onChanged: (value) {
                setState(() {
                  currentText = value;
                });
              },
              beforeTextPaste: (text) {
                return true;
              },
            ),
          ),
          SizedBox(
            width: Get.width - 120,
            child: ElevatedButton(
                onPressed: () async {
                  final result = await AuthApi().getOTP(id, currentText);
                  if (result != "error") {
                    prefs!.setString("token", result['token']);
                    prefs!.setString("phone", result['phone']);
                    prefs!.setString("username", result['name']);
                    prefs!.setInt("deliveryCost", result['deliverCost']);
                    prefs!.setString("id", id);
                    prefs!.setBool("isLogined", true);
                    prefs!.setBool("isGuest", false);
                    Get.to(const EndAuthScreen());
                  } else {
                    Get.snackbar('Error', "Otp not correct",
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                        snackPosition: SnackPosition.BOTTOM);
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.all(15.0),
                  child: Text(
                    "Verify",
                    style: TextStylesManagment.button,
                  ),
                )),
          ),
          const SizedBox(
            height: 15,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                "Did not recieve ",
                style: TextStylesManagment.caption,
              ),
              InkWell(
                onTap: _handleResendOtp,
                child: Text(
                  'Resend',
                  style: TextStylesManagment.primaryBody,
                ),
              )
            ],
          ),
          if (!canResend)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'You can resend OTP in ${_remaining.inMinutes.toString().padLeft(2, '0')}:${(_remaining.inSeconds % 60).toString().padLeft(2, '0')}',
                style: const TextStyle(color: Colors.orange, fontSize: 14),
              ),
            ),
        ],
      ),
    );
  }
}
