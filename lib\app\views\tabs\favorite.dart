import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/app/views/widgets/cached_image.dart';

import '../../../configs/colors.dart';
import '../../../configs/constants.dart';
import '../../../configs/text_style.dart';
import '../../../main.dart';
import '../../controllers/controllers/products_controller.dart';

class FavoriteScreen extends StatefulWidget {
  const FavoriteScreen({super.key});

  @override
  State<FavoriteScreen> createState() => _FavoriteScreenState();
}

class _FavoriteScreenState extends State<FavoriteScreen> {
  late List<String> favoriteProducts;
  ProductsController productsController = Get.find();

  @override
  void initState() {
    favoriteProducts = prefs!.getStringList('favoriteProducts') ?? [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(5),
      child: favoriteProducts.isEmpty
          ? Center(
              child: Text("Your favorite list is empty.".tr,
                  style: TextStylesManagment.medTitle))
          : ListView.builder(
              itemCount: favoriteProducts.length,
              itemBuilder: (context, index) {
                final product = productsController.products
                    .where((item) => item.id == favoriteProducts[index])
                    .toList();
                return product.isNotEmpty
                    ? Container(
                        margin: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 15),
                        decoration: BoxDecoration(
                            border: Border.all(
                                color: ColorManagement.lightPrimary,
                                width: 0.2),
                            boxShadow: [
                              BoxShadow(
                                  spreadRadius: 3,
                                  blurRadius: 2,
                                  color: Colors.grey.withValues(alpha: 0.2))
                            ],
                            color: ColorManagement.primary,
                            borderRadius: BorderRadius.circular(10)),
                        child: Column(
                          children: [
                            // Divider(
                            //     height: 1,
                            //     color:
                            //         ColorManagement.secondoryColor.withOpacity(0.2)),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 8),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Row(
                                      children: [
                                        Container(
                                          margin:
                                              const EdgeInsets.only(left: 8),
                                          width: 90,
                                          height: 90,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            image: DecorationImage(
                                              image: CachedImage(
                                                      imageLink: Constants()
                                                              .imagerUrl +
                                                          product.first.images
                                                              .split('|')[0])
                                                  .cachedImage(),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                ProductApi().switchName(
                                                    product.first.name),
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                ProductApi().switchName(
                                                    product.first.info),
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.white,
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ],
                                          ),
                                        ),
                                        const SizedBox(width: 15),
                                      ],
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: () async {
                                          favoriteProducts
                                              .remove(product.first.id);
                                          await prefs!.setStringList(
                                              'favoriteProducts',
                                              favoriteProducts);
                                          setState(() {});
                                        },
                                        icon: const Icon(
                                          Icons.favorite,
                                          color: Colors.orange,
                                        ),
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          if (productsController.cartItems
                                              .containsKey(product.first.id)) {
                                            // Remove from cart if it exists
                                            productsController.removeFromCart(
                                                product.first.id);
                                            Get.snackbar(
                                              "Removed",
                                              "Product has been removed from cart",
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              backgroundColor: Colors.red,
                                              colorText: Colors.white,
                                            );
                                          } else {
                                            // Add to cart if not already in it
                                            productsController.addToCart(
                                                product.first.id, 1);
                                            Get.snackbar(
                                              "Success",
                                              "Product has been added to cart",
                                              snackPosition:
                                                  SnackPosition.BOTTOM,
                                              backgroundColor: Colors.green,
                                              colorText: Colors.white,
                                            );
                                          }
                                        },
                                        icon: Obx(() => Icon(
                                              Icons.shopping_cart_checkout,
                                              color: productsController
                                                      .cartItems
                                                      .containsKey(
                                                          product.first.id)
                                                  ? Colors.red[400]
                                                  : Colors.white,
                                            )),
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            ),
                            Divider(
                                height: 1,
                                color: ColorManagement.secondoryColor
                                    .withValues(alpha: 0.2))
                          ],
                        ),
                      )
                    : Container();
              }),
    );
  }
}
