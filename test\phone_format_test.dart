// Test file to verify phone number formatting
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zamin_phone/app/controllers/services/auth.dart';
import 'package:zamin_phone/app/models/country_codes.dart';
import 'package:zamin_phone/main.dart';

void main() {
  group('Phone number formatting tests', () {
    setUpAll(() async {
      // Mock shared preferences for testing
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
    });

    test('reformatPhoneNumber works correctly', () {
      // Test the reformatPhoneNumber function with different country codes
      AuthApi authApi = AuthApi();

      // Test with Iraq (964)
      String phoneIraq = authApi.reformatPhoneNumber("7501234567", 1, "964");
      expect(phoneIraq, equals("9647501234567"));

      // Test with USA (1)
      String phoneUSA = authApi.reformatPhoneNumber("2551234567", 1, "1");
      expect(phoneUSA, equals("12551234567"));

      // Test with UK (44)
      String phoneUK = authApi.reformatPhoneNumber("7123456789", 1, "44");
      expect(phoneUK, equals("447123456789"));

      // Test with existing country code
      String phoneExisting =
          authApi.reformatPhoneNumber("9647501234567", 1, "964");
      expect(phoneExisting, equals("9647501234567"));

      // Test country codes loading
      List<CountryCode> countries = CountryCodes.getCountryCodes();
      expect(countries.length, greaterThan(0));
      expect(countries.first.name, isNotEmpty);
      expect(countries.first.code, isNotEmpty);
    });
  });
}
