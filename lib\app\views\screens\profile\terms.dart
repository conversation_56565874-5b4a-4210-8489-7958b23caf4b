import 'package:flutter/material.dart';
import 'package:zamin_phone/app/views/screens/profile/docs/terms.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';
import 'package:zamin_phone/configs/colors.dart';

import '../../../../main.dart';

class TermsAndConditionsPage extends StatelessWidget {
  const TermsAndConditionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = prefs!.getString('lang');
    String terms;

    switch (lang) {
      case "ar":
        terms = Terms().ar;
        break;
      case "en":
        terms = Terms().en;
        break;
      case "so":
        terms = Terms().so;
        break;
      default:
        terms = Terms().ku;
    }
    return Scaffold(
      backgroundColor: ColorManagement.primary,
      appBar: AppBars().screensAppBar(""),
      body: Directionality(
        textDirection: lang == "en" ? TextDirection.ltr : TextDirection.rtl,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Text(
              terms,
              textAlign: TextAlign.start,
              style: const TextStyle(
                  fontSize: 16, height: 1.5, color: Colors.white),
            ),
          ),
        ),
      ),
    );
  }
}
