import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/views/main_tab.dart';

import '../../../configs/colors.dart';
import '../../../main.dart';
import '../../controllers/services/auth.dart';
import '../../models/country_codes.dart';
import '../widgets/textfield.dart';

class CheckGoogleSign extends StatefulWidget {
  final String token;
  final String title;
  final String id;
  const CheckGoogleSign(this.token, this.title, this.id, {super.key});

  @override
  State<CheckGoogleSign> createState() => _CheckGoogleSignState();
}

class _CheckGoogleSignState extends State<CheckGoogleSign> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  CountryCode selectedCountryCode = CountryCodes.getCountryCodes().first;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      appBar: AppBar(
          foregroundColor: Colors.orange,
          backgroundColor: ColorManagement.secondoryColor,
          title: Text(widget.title)),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            textField(_usernameController, "Username", TextInputType.text),
            const SizedBox(height: 20),
            Row(
              children: [
                // Country Code Dropdown
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white, width: 1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<CountryCode>(
                      value: selectedCountryCode,
                      isDense: true,
                      dropdownColor: ColorManagement.secondoryColor,
                      icon: const Icon(Icons.keyboard_arrow_down,
                          color: Colors.white, size: 16),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      items: CountryCodes.getCountryCodes()
                          .map((CountryCode country) {
                        return DropdownMenuItem<CountryCode>(
                          value: country,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(country.flag,
                                  style: const TextStyle(fontSize: 16)),
                              const SizedBox(width: 8),
                              Text('+${country.code}',
                                  style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold)),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (CountryCode? newValue) {
                        if (newValue != null) {
                          setState(() {
                            selectedCountryCode = newValue;
                          });
                          if (kDebugMode) {
                            print(
                                "Selected country: ${newValue.name} (+${newValue.code})");
                          }
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                    child: textField(
                  _phoneController,
                  "Phone : optional",
                  TextInputType.number,
                )),
              ],
            ),
            const SizedBox(height: 30),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () async {
                  // Handle submit logic here
                  final username = _usernameController.text.trim();

                  if (username.isEmpty) {
                    Get.snackbar('Error', "Fill all fields",
                        backgroundColor: Colors.red,
                        colorText: Colors.black,
                        snackPosition: SnackPosition.BOTTOM);
                  } else {
                    final phoneNumber = AuthApi().reformatPhoneNumber(
                        _phoneController.text, 1, selectedCountryCode.code);
                    final result = await AuthApi().updateClient(
                        _usernameController.text,
                        _phoneController.text,
                        widget.token);

                    if (result['status']) {
                      prefs!.setString("token", widget.token);
                      prefs!.setString("phone", phoneNumber);
                      prefs!.setString("username", _usernameController.text);
                      prefs!.setString("id", widget.id);
                      prefs!.setBool("isLogined", true);
                      prefs!.setBool("isGuest", false);
                      
                      Get.snackbar('Great!', result['message'],
                          backgroundColor: Colors.green,
                          colorText: Colors.black,
                          snackPosition: SnackPosition.BOTTOM);

                      Get.to(const MainTab());
                    } else {
                      Get.snackbar('Error', result['message'],
                          backgroundColor: Colors.red,
                          colorText: Colors.white,
                          snackPosition: SnackPosition.BOTTOM);
                    }
                  }
                },
                child: const Text('Submit'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
