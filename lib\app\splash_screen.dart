import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import 'package:zamin_phone/app/controllers/controllers/main_controller.dart';
import 'package:zamin_phone/app/controllers/controllers/products_controller.dart';
import 'package:zamin_phone/app/controllers/services/auth.dart';
import 'package:zamin_phone/app/controllers/services/notifications.dart';
import 'package:zamin_phone/app/views/main_tab.dart';
import '../main.dart';
import 'controllers/services/update.dart';
import 'views/auth/start.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  bool? isLogined;
  late VideoPlayerController _controller;
  ProductsController productsController = Get.put(ProductsController());
  MainController mainController = Get.put(MainController());
  NotificationsService notificationsService = NotificationsService();

  @override
  void initState() {
    super.initState();

    isLogined = prefs!.getBool('isLogined');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      checkForceUpdate(context);
    });
    _checkLoginStatus();
    _controller = VideoPlayerController.asset('assets/loading1.mp4');
    _controller.addListener(() {
      if (!mounted) return;
      setState(() {});
    });
    _controller.setLooping(true);
    _controller.initialize().then((_) {
      if (!mounted) return;
      setState(() {});
    });
    _controller.play().then((_) {});
    AuthApi().determinePosition();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _checkLoginStatus() async {
    await Future.delayed(const Duration(seconds: 6));
    if (isLogined == null || !isLogined!) {
      Get.off(() => const LetsStart());
    } else {
      Get.off(() => const MainTab());
    }
    //Get.off(() => const MainTab());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: AspectRatio(
          aspectRatio: _controller.value.aspectRatio,
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: <Widget>[
              VideoPlayer(_controller),
            ],
          ),
        ),
      ),
    );
  }
}

//===============
