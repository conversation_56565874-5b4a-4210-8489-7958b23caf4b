import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  Future<Map<String, dynamic>> signInWithGoogleAndSendToBackend(
      {
      // required String username,
      required String? fcmToken,
      required String apiUrl,
      required double lat,
      required double long}) async {
    try {
      // Step 1: Sign in with Google
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return {"error": "nog google account found"};

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Step 2: Sign in with Firebase
      UserCredential userCredential =
          await _auth.signInWithCredential(credential);

      // Step 3: Get Firebase ID token
      String? firebaseToken = await userCredential.user?.getIdToken();
      //print(firebaseToken);
      // Step 4: Send token + username + fcmToken to backend
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          "accountToken": firebaseToken,
          "notificationToken": fcmToken,
          "latitude": lat,
          "longitude": long
          //"username": username,
        }),
      );

      return jsonDecode(response.body); // Or extract token, session, etc.
    } catch (e) {
      Get.snackbar(
        "Error",
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return {"error": e};
    }
  }

  Future<Map<String, dynamic>> signInWithAppleAndSendToBackend(
      {
      // required String username,
      required String? fcmToken,
      required String apiUrl,
      required double lat,
      required double long}) async {
    try {
      // Step 1: Sign in with Google
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthProvider = OAuthProvider('apple.com');
      final credential = oauthProvider.credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Step 2: Sign in with Firebase
      UserCredential userCredential =
          await _auth.signInWithCredential(credential);

      // Step 3: Get Firebase ID token
      String? firebaseToken = await userCredential.user?.getIdToken();

      // Step 4: Send token + username + fcmToken to backend
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          "accountToken": firebaseToken,
          "notificationToken": fcmToken,
          "latitude": lat,
          "longitude": long
          //"username": username,
        }),
      );

      return jsonDecode(response.body); // Or extract token, session, etc.
    } catch (e) {
      return {"error": e};
    }
  }
}
