import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/controllers/products_controller.dart';
import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/configs/colors.dart';
import 'package:zamin_phone/configs/text_style.dart';
import 'package:zamin_phone/main.dart';

import '../../../configs/constants.dart';

class CartScreen extends StatelessWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final deliveryCost = prefs!.getInt("deliveryCost");
    print(deliveryCost);

    final ProductsController cartController = Get.find();
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      body: Obx(() {
        if (cartController.cartItems.isEmpty) {
          return Center(
              child: Text(
            "Your cart is empty.".tr,
            style: TextStylesManagment.medTitle,
          ));
        }
        return SingleChildScrollView(
          child: Column(
            children: [
              GestureDetector(
                onTap: () =>
                    _confirmOrder(context, cartController, deliveryCost),
                child: Container(
                  // height: 80,
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                  decoration: BoxDecoration(
                      border: Border.all(
                          color: ColorManagement.lightPrimary, width: 0.2),
                      boxShadow: [
                        BoxShadow(
                            spreadRadius: 3,
                            blurRadius: 2,
                            color: Colors.grey.withValues(alpha: 0.2))
                      ],
                      color: ColorManagement.primary,
                      borderRadius: BorderRadius.circular(10)),
                  child: Text(
                    "Place Order".tr,
                    textAlign: TextAlign.center,
                    style: TextStylesManagment.medTitle,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(3.0),
                child: Column(
                  children: cartController.cartItems.entries.map((entry) {
                    final product = cartController.products
                        .where((item) => item.id == entry.key);
                    final num disc = product.first.discount;
                    return product.isNotEmpty
                        ? Container(
                            margin: const EdgeInsets.symmetric(
                                vertical: 5, horizontal: 15),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: ColorManagement.lightPrimary,
                                    width: 0.2),
                                boxShadow: [
                                  BoxShadow(
                                      spreadRadius: 3,
                                      blurRadius: 2,
                                      color: Colors.grey.withValues(alpha: 0.2))
                                ],
                                color: ColorManagement.primary,
                                borderRadius: BorderRadius.circular(10)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(
                                            left: 5, top: 5),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: Image.network(
                                            Constants().imagerUrl +
                                                product.first.images
                                                    .split('|')[0],
                                            width: 50,
                                            height: 50,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                      Text(
                                        ProductApi()
                                            .switchName(product.first.name),
                                        style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.remove_circle,
                                            color: Colors.orange),
                                        onPressed: () {
                                          cartController
                                              .removeFromCart(product.first.id);
                                        },
                                      ),
                                    ]),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    // Leading image

                                    const SizedBox(width: 10),

                                    // Title and Quantity
                                    Expanded(
                                      child: Row(
                                        children: [
                                          const Text(
                                            "Qty.: ",
                                            style:
                                                TextStyle(color: Colors.white),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.remove,
                                                color: Colors.orange),
                                            onPressed: () {
                                              cartController.decreaseQuantity(
                                                  product.first.id);
                                            },
                                          ),
                                          Text(
                                            "${entry.value}",
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.add,
                                                color: Colors.white),
                                            onPressed: () {
                                              cartController.increaseQuantity(
                                                  product.first.id);
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    Text(
                                        " Price: ${product.first.price} IQD   ",
                                        style: const TextStyle(
                                            color: Colors.white)),

                                    // Trailing remove button
                                  ],
                                ),
                                // Row(
                                //   mainAxisAlignment: MainAxisAlignment.start,
                                //   children: [
                                //     const SizedBox(
                                //       width: 10,
                                //     ),
                                //     const Text('Delivery Cost  :  ',
                                //         style: TextStyle(color: Colors.white)),
                                //     Text('$deliveryCost',
                                //         style: const TextStyle(color: Colors.white))
                                //   ],
                                // ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text("   Discount  : $disc %",
                                        style: const TextStyle(
                                            color: Colors.white)),
                                    Text(
                                        " Total  Price: ${cartController.getTotalPrice(product.first.id, entry.value)}  IQD",
                                        style: const TextStyle(
                                            color: Colors.white)),
                                  ],
                                )
                              ],
                            ))
                        : Container();
                  }).toList(),
                ),
              ),
            ],
          ),
        );
      }),
      bottomNavigationBar: Obx(() {
        return cartController.cartItems.isNotEmpty
            ? Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 30.0, horizontal: 50),
                child: GestureDetector(
                  onTap: () =>
                      _confirmOrder(context, cartController, deliveryCost),
                  child: Container(
                    // height: 80,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    decoration: BoxDecoration(
                        border: Border.all(
                            color: ColorManagement.lightPrimary, width: 0.2),
                        boxShadow: [
                          BoxShadow(
                              spreadRadius: 3,
                              blurRadius: 2,
                              color: Colors.grey.withValues(alpha: 0.2))
                        ],
                        color: ColorManagement.primary,
                        borderRadius: BorderRadius.circular(10)),
                    child: Text(
                      "Place Order".tr,
                      textAlign: TextAlign.center,
                      style: TextStylesManagment.medTitle,
                    ),
                  ),
                ))
            : const SizedBox.shrink();
      }),
    );
  }

  Row orederAndDeliveryCostMethod(
      ProductsController cartController, text, cost) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(text,
            style: TextStyle(fontSize: 14, color: ColorManagement.primary)),
        Text("  $cost IQD",
            style: TextStyle(
                fontSize: 14,
                color: ColorManagement.primary,
                fontWeight: FontWeight.bold)),
      ],
    );
  }

  _confirmOrder(context, controller, deliveryCost) {
    return showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(27.5),
          topRight: Radius.circular(27.5),
        ),
      ),
      builder: (context) {
        return Container(
          height: 250,
          constraints: const BoxConstraints(),
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min, // Adjust size based on content
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    orederAndDeliveryCostMethod(controller, "Order Cost  :",
                        controller.getTotalAmount()),
                    orederAndDeliveryCostMethod(
                        controller, "Delivery Cost  :", deliveryCost),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text("Total Cost  :",
                            style: TextStylesManagment.body),
                        Text(
                            "  ${deliveryCost + controller.getTotalAmount()} IQD",
                            style: TextStylesManagment.body),
                      ],
                    ),
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                Text(
                  'Are You Sure?'.tr,
                  style: TextStylesManagment.primaryLarg,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    TextButton(
                      onPressed: () {
                        // Place order logic
                        if (prefs!.getBool("isLogined")!) {
                          ProductApi()
                              .sendCartDataToAPI(controller.getCartData());
                          controller.clearCart();
                        } else {
                          Get.snackbar("Error", "Login to place order",
                              snackPosition: SnackPosition.BOTTOM,
                              backgroundColor: Colors.red,
                              colorText: Colors.white);
                        }

                        Navigator.pop(context);
                      },
                      child: Text('Yes'.tr),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text('No'.tr),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
