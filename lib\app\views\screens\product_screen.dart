import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/controllers/products_controller.dart';
import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/app/models/data.dart';
import 'package:zamin_phone/app/views/widgets/cached_image.dart';
import 'package:zamin_phone/app/views/widgets/product_wedgit.dart';
import 'package:zamin_phone/configs/constants.dart';
import 'package:zamin_phone/configs/text_style.dart';

import '../../../configs/colors.dart';
import '../../../main.dart';
import '../widgets/app_bar.dart';

class SingleProductScreen extends StatefulWidget {
  final Product product;
  final List<Product> products;
  final Map categoryName;
  const SingleProductScreen(this.product, this.products, this.categoryName,
      {super.key});

  @override
  State<SingleProductScreen> createState() => _SingleProductScreenState();
}

class _SingleProductScreenState extends State<SingleProductScreen> {
  late Product currentProduct;
  late List<String> favoriteProducts;
  late List<String> images;
  ProductsController productsController = Get.find();
  int currentIndex = 0;
  @override
  void initState() {
    currentProduct = widget.product;
    favoriteProducts = prefs!.getStringList('favoriteProducts') ?? [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    images = currentProduct.images.split('|');
    print(currentProduct.images);

    //final prod = [];
    final relatedProducts = widget.products.toList();
    //  ProductApi().getSimilarProductName(widget.product, widget.products);
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      appBar: AppBars().screensAppBar("Product Details".tr),
      body: Directionality(
        textDirection: prefs!.getString('lang') == 'en'
            ? TextDirection.ltr
            : TextDirection.rtl,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: ListView(
            children: [
              CarouselSlider(
                  items: images.map((i) {
                    return Builder(
                      builder: (BuildContext context) {
                        return Container(
                            width: MediaQuery.of(context).size.width,
                            margin: const EdgeInsets.all(10.0),
                            decoration: BoxDecoration(
                                image: DecorationImage(
                                    image: CachedImage(
                                            imageLink:
                                                Constants().imagerUrl + i)
                                        .cachedImage(),
                                    fit: BoxFit.fill),
                                boxShadow: [
                                  BoxShadow(
                                      spreadRadius: 3,
                                      blurRadius: 2,
                                      color: Colors.grey.withValues(alpha: 0.2))
                                ],
                                color: const Color(0xFFF7F9FB),
                                borderRadius: BorderRadius.circular(10)));
                      },
                    );
                  }).toList(),
                  options: CarouselOptions(
                      viewportFraction: 1,
                      height: 250,
                      autoPlay: true,
                      onPageChanged: (v, _) {
                        setState(() {
                          currentIndex = v;
                        });
                      })),
              carouselIndicatior(images.length),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "${ProductApi().switchName(widget.categoryName['cat'])} / ${ProductApi().switchName(widget.categoryName['name'])}",
                  style: TextStylesManagment.categories,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Text(ProductApi().switchName(currentProduct.name),
                    style: TextStylesManagment.medTitle),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: currentProduct.discount > 0
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                "   Old Price :  ".tr,
                                style: TextStylesManagment.primarySmall,
                              ),
                              Stack(
                                alignment: Alignment.centerLeft,
                                children: [
                                  Text(
                                    '${currentProduct.price + currentProduct.discount} IQD',
                                    style: TextStylesManagment.primarySmall
                                        .copyWith(
                                      color: Colors.grey,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                  Positioned.fill(
                                    child: CustomPaint(
                                      painter:
                                          _OrangeItalicStrikeThroughPainter(),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          Row(children: [
                            Text(
                              "   New Price :  ".tr,
                              style: TextStylesManagment.primarySmall,
                            ),
                            Text(
                              // Calculate discounted price
                              '${(currentProduct.price).toString()} IQD',
                              style: TextStylesManagment.primaryMed.copyWith(
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ]),
                        ],
                      )
                    : Text(
                        '${currentProduct.price} IQD',
                        style: TextStylesManagment.primaryMed,
                      ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          onPressed: () {
                            toggleFavorite(currentProduct.id);
                          },
                          icon: Icon(
                            favoriteProducts.contains(currentProduct.id)
                                ? Icons.favorite
                                : Icons.favorite_outline,
                            color: favoriteProducts.contains(currentProduct.id)
                                ? Colors.red
                                : Colors.white,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            if (productsController.cartItems
                                .containsKey(currentProduct.id)) {
                              // Remove from cart if it exists
                              productsController
                                  .removeFromCart(currentProduct.id);
                              Get.snackbar(
                                "Removed",
                                "Product has been removed from cart",
                                snackPosition: SnackPosition.BOTTOM,
                                backgroundColor: Colors.red,
                                colorText: Colors.white,
                              );
                            } else {
                              // Add to cart if not already in it
                              productsController.addToCart(
                                  currentProduct.id, 1);
                              Get.snackbar(
                                "Success",
                                "Product has been added to cart",
                                snackPosition: SnackPosition.BOTTOM,
                                backgroundColor: Colors.green,
                                colorText: Colors.white,
                              );
                            }
                          },
                          icon: Obx(() => Icon(
                                Icons.shopping_cart_checkout,
                                color: productsController.cartItems
                                        .containsKey(currentProduct.id)
                                    ? Colors.red[400]
                                    : Colors.white,
                              )),
                        )
                      ],
                    ),
                    FutureBuilder(
                        future: ProductApi().getProductRate(currentProduct.id),
                        builder: (context, AsyncSnapshot snapshot) {
                          if (snapshot.hasData) {
                            final rating = snapshot.data.toString();
                            return Row(
                              children: [
                                Text(
                                  "$rating  ",
                                  style: const TextStyle(color: Colors.white),
                                ),
                                GestureDetector(
                                  onTap: () async {
                                    if (prefs!.getBool("isLogined")!) {
                                      _rateProduct(context, currentProduct.id);
                                    } else {
                                      Get.snackbar(
                                          "Error", "Login to rate product",
                                          snackPosition: SnackPosition.BOTTOM,
                                          backgroundColor: Colors.red,
                                          colorText: Colors.white);
                                    }
                                  },
                                  child: RatingBarIndicator(
                                    unratedColor: Colors.white38,
                                    itemSize: 20,
                                    itemCount: 5,
                                    rating: double.parse(rating),
                                    itemBuilder: (context, index) {
                                      return const Icon(Icons.star,
                                          color: Colors.yellow);
                                    },
                                  ),
                                )
                              ],
                            );
                          } else {
                            return const Text('',
                                style: TextStyle(color: Colors.white));
                          }
                        })
                  ],
                ),
              ),
              Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: Text(
                    ProductApi().switchName(currentProduct.info),
                    textAlign: TextAlign.justify,
                    style: const TextStyle(color: Colors.grey),
                  )

                  // Text(ProductApi().switchName(currentProduct.info)),
                  ),
              Padding(
                padding: const EdgeInsets.only(
                    top: 8.0, left: 8, right: 8, bottom: 10),
                child: Text(
                  'Related Products :'.tr,
                  style: TextStylesManagment.medTitle,
                ),
              ),
              GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      childAspectRatio: 0.9,
                      mainAxisSpacing: 20,
                      crossAxisSpacing: 20,
                      crossAxisCount: 2),
                  shrinkWrap: true,
                  physics: const ScrollPhysics(),
                  itemCount: relatedProducts.length,
                  itemBuilder: (context, index) {
                    final prod = relatedProducts[index];
                    return Container(
                        decoration: BoxDecoration(
                            // border: Border.all(width: 1, color: Colors.amber[200]!),
                            boxShadow: [
                              BoxShadow(
                                  spreadRadius: 3,
                                  blurRadius: 2,
                                  color: Colors.grey.withValues(alpha: 0.2))
                            ],
                            // border: Border.all(
                            //     width: 1, color: ColorManagement.secondoryColor),
                            color: ColorManagement.primary,
                            borderRadius: BorderRadius.circular(15)),
                        //margin: const EdgeInsets.all(5),
                        child: GestureDetector(
                            onTap: () {
                              // setState(() {
                              //   currentProduct = relatedProducts[index];
                              // });
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => SingleProductScreen(
                                      relatedProducts[index],
                                      widget.products,
                                      widget.categoryName),
                                ),
                              );
                              // Get.off(() => SingleProductScreen(
                              //     relatedProducts[index],
                              //     widget.products,
                              //     widget.categoryName));
                            },
                            child: ProductWedgit(prod)));
                  })
            ],
          ),
        ),
      ),
    );
  }

  void toggleFavorite(String articleId) async {
    if (favoriteProducts.contains(articleId)) {
      favoriteProducts.remove(articleId);
    } else {
      favoriteProducts.add(articleId);
    }
    await prefs!.setStringList('favoriteProducts', favoriteProducts);
    setState(() {});
  }

  carouselIndicatior(length) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          for (int i = 0; i < length; i++)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 3),
              width: 10,
              height: 10,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: currentIndex == i
                    ? Colors.orange
                    : ColorManagement.lightPrimary,
              ),
            )
        ],
      ),
    );
  }

  void _rateProduct(BuildContext context, productId) {
    int currentRating = 3;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Center(
            child: Text(
              'Rate Your Experience',
              style: TextStyle(
                fontSize: 20.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min, // Avoid potential overflow
            children: [
              RatingBar.builder(
                initialRating: 3,
                minRating: 1,
                direction: Axis.horizontal,
                allowHalfRating: false,
                itemCount: 5,
                itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                itemBuilder: (context, index) => Icon(
                  Icons.star,
                  size: 32.0, // Larger stars for better visibility
                  color: index < currentRating
                      ? Colors.amber
                      : Colors.grey[400], // Gradient effect
                ),
                onRatingUpdate: (rating) {
                  setState(() {
                    currentRating = rating.toInt();
                  });
                },
              ),
              const SizedBox(height: 16.0), // Add spacing for better layout
              ElevatedButton(
                onPressed: () {
                  ProductApi().rateProduct(productId, currentRating);
                  Navigator.pop(context); // Close the dialog
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      Colors.orange, // Match star color for consistency
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                ),
                child: Text(
                  'Submit Rating'.tr,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0), // Rounded corners
          ),
        );
      },
    );
  }
}

class _OrangeItalicStrikeThroughPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    // Draw an italic (slanted) line
    final double slant = size.height * 0.3;
    canvas.drawLine(
      Offset(0, size.height / 2 + slant),
      Offset(size.width, size.height / 2 - slant),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
