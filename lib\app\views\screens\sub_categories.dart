import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/app/views/screens/products.dart';
import 'package:zamin_phone/app/views/widgets/app_bar.dart';

import '../../../configs/colors.dart';
import '../../../configs/text_style.dart';
import '../../controllers/controllers/products_controller.dart';

class Subcategories extends StatelessWidget {
  final String categoryId;
  final String categoryName;
  const Subcategories(this.categoryId, this.categoryName, {super.key});

  @override
  Widget build(BuildContext context) {
    ProductsController productsController = Get.find();
    final categorySubs = productsController.getSubByCategoryId(categoryId);
    return Scaffold(
      backgroundColor: ColorManagement.secondoryColor,
      appBar: AppBars().screensAppBar(ProductApi().switchName(categoryName)),
      body: ListView.builder(
          shrinkWrap: true,
          physics: const ScrollPhysics(),
          itemCount: categorySubs.length,
          itemBuilder: (context, index) {
            final data = categorySubs[index];
            return Container(
              margin: const EdgeInsets.all(15.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                      spreadRadius: 3,
                      blurRadius: 2,
                      color: Colors.grey.withValues(alpha: 0.2))
                ],
                color: ColorManagement.primary,
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: ListTile(
                  onTap: () => Get.to(ProductsScreen(
                    data.id,
                    {"name": data.name, "cat": categoryName},
                  )),
                  title: Text(
                    ProductApi().switchName(data.name),
                    style: TextStylesManagment.productTitle,
                  ),
                ),
              ),
            );
          }),
    );
  }
}
