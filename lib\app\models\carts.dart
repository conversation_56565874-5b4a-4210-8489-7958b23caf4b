import 'dart:convert';

List<Cart> cartFromJson(String str) =>
    List<Cart>.from(json.decode(str).map((x) => Cart.fromJson(x)));

String cartToJson(List<Cart> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Cart {
  final int id;
  final String name;
  final String model;
  final int price;
  final String imageLink;
  final int quantity;

  Cart(
      {required this.id,
      required this.name,
      required this.model,
      required this.price,
      required this.imageLink,
      required this.quantity});

  factory Cart.fromJson(Map<String, dynamic> json) => Cart(
        id: json["id"],
        name: json["name"],
        model: json["model"],
        price: json["price"],
        imageLink: json["image_link"],
        quantity: json["quantity"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "model": model,
        "price": price,
        "image_link": imageLink,
        "quantity": quantity
      };
}
