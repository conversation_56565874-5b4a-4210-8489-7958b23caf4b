import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/views/auth/register.dart';

import '../../../configs/colors.dart';
import '../../../configs/text_style.dart';
import '../../../main.dart';
import '../../controllers/services/auth.dart';

class LetsStart extends StatefulWidget {
  const LetsStart({super.key});

  @override
  State<LetsStart> createState() => _LetsStartState();
}

class _LetsStartState extends State<LetsStart> with WidgetsBindingObserver {
  bool _isLocationInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeLocation();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && !_isLocationInitialized) {
      // App resumed from settings, try to get location again
      _initializeLocation();
    }
  }

  _initializeLocation() async {
    try {
      final result = await AuthApi().determinePosition();

      if (result is Map) {
        if (result['success'] == true) {
          final lat = prefs!.getDouble('Latitude');
          final long = prefs!.getDouble('Longitude');

          if (lat != null && long != null) {
            setState(() {
              _isLocationInitialized = true;
            });
          } else {
          }
        } else if (result['error'] != null) {
          _handleLocationError(result);
        }
      }
    } catch (e) {
      _showLocationErrorDialog("Unexpected Error",
          "An unexpected error occurred while getting location: $e");
    }
  }

  void _handleLocationError(Map result) {
    String errorType = result['error'];
    String message = result['message'] ?? 'Unknown error occurred';

    switch (errorType) {
      case 'location_disabled':
        _showLocationSettingsDialog();
        break;
      case 'permission_denied':
        _showPermissionDialog();
        break;
      case 'permission_denied_forever':
        _showAppSettingsDialog();
        break;
      default:
        _showLocationErrorDialog("Location Error", message);
    }
  }

  void _showLocationSettingsDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Location Services Disabled'),
          content: const Text(
            'Location services are turned off. Please enable location services in your device settings to continue.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await AuthApi().openLocationSettings();
                // Wait a bit and try again
                Future.delayed(const Duration(seconds: 2), () {
                  _initializeLocation();
                });
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Location Permission Required'),
          content: const Text(
            'This app needs location permission to provide location-based services. Please grant location permission.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _initializeLocation(); // Try again
              },
              child: const Text('Retry'),
            ),
          ],
        );
      },
    );
  }

  void _showAppSettingsDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permission Required'),
          content: const Text(
            'Location permission has been permanently denied. Please enable it in app settings to continue.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await AuthApi().openAppSettings();
                // Wait a bit and try again
                Future.delayed(const Duration(seconds: 2), () {
                  _initializeLocation();
                });
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  void _showLocationErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _initializeLocation(); // Try again
              },
              child: const Text('Retry'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: ColorManagement.secondoryColor,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'Zamin',
                style: TextStylesManagment.primaryExtraLarg,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'Welcome to ',
                    style: TextStylesManagment.caption,
                  ),
                  Text(
                    "Zamin",
                    style: TextStylesManagment.primaryBody,
                  ),
                  const Text(
                    "! Let's shopping together",
                    style: TextStylesManagment.caption,
                  ),
                ],
              ),
              const SizedBox(height: 20),
              // Location status indicator
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 40),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: _isLocationInitialized
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.orange.withValues(alpha: 0.1),
                  border: Border.all(
                    color:
                        _isLocationInitialized ? Colors.green : Colors.orange,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _isLocationInitialized
                          ? Icons.location_on
                          : Icons.location_off,
                      color:
                          _isLocationInitialized ? Colors.green : Colors.orange,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _isLocationInitialized
                            ? 'Location access enabled'
                            : 'Setting up location access...',
                        style: TextStyle(
                          color: _isLocationInitialized
                              ? Colors.green
                              : Colors.orange,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    if (!_isLocationInitialized)
                      const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.orange),
                        ),
                      ),
                  ],
                ),
              ),
              SizedBox(
                height: Get.height * 0.30,
              ),
              SizedBox(
                width: Get.width - 120,
                child: ElevatedButton(
                    onPressed: _isLocationInitialized
                        ? () {
                            Get.off(const AuthScreen());
                          }
                        : () {
                            _initializeLocation(); // Try to get location again
                          },
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          _isLocationInitialized ? null : Colors.orange,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _isLocationInitialized
                            ? "Let's Start"
                            : "Setup Location",
                        style: TextStylesManagment.button,
                      ),
                    )),
              ),
              const SizedBox(
                height: 25,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
