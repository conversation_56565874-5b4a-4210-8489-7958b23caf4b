import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zamin_phone/app/views/screens/sub_categories.dart';
import 'package:zamin_phone/app/views/widgets/cached_image.dart';
import '../../../configs/constants.dart';
import '../../controllers/controllers/products_controller.dart';

class CategoriesScreen extends StatelessWidget {
  const CategoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    ProductsController productsController = Get.find();
    return Padding(
      padding: const EdgeInsets.all(15.0),
      child: GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              mainAxisSpacing: 20, crossAxisCount: 4, crossAxisSpacing: 20),
          shrinkWrap: true,
          physics: const ScrollPhysics(),
          itemCount: productsController.categories.length,
          itemBuilder: (context, index) {
            final data = productsController.categories[index];
            final imageData;
            if (data.image != null) {
              imageData =
                  CachedImage(imageLink: Constants().imagerUrl + data.image!)
                      .cachedImage();
            } else {
              imageData = const AssetImage('assets/logo.png');
            }
            return GestureDetector(
              onTap: () => Get.to(Subcategories(data.id, data.name)),
              child: Container(
                decoration: BoxDecoration(
                    border: Border.all(width: 2, color: Colors.orange),
                    borderRadius: BorderRadius.circular(40),
                    color: Colors.white),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(40),
                  child: Image(
                    image: imageData,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            );
          }),
    );
  }
}
