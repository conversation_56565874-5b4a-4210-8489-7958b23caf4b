import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../../../main.dart';
import '../controllers/notification.dart';

class NotificationsService {
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  void requestNotificationPermission() async {
    NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: true,
        badge: true,
        carPlay: true,
        criticalAlert: true,
        provisional: true,
        sound: true);
    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      //print('User granted permission: ${settings.authorizationStatus}');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      //print(
      //'User granted provisional permission: ${settings.authorizationStatus}');
    } else {
      // print('User granted permission: ${settings.authorizationStatus}');
    }
  }

  Future<void> getToken() async {
    String? token = await messaging.getToken();
    if (token != null) {
      if (kDebugMode) {
        print('FCM Token: $token');
      }
      prefs!.setString('fcm_token', token);
    }
  }

  void listenToNotifications() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      String? imageUrl = message.notification!.android?.imageUrl ??
          message.notification!.apple?.imageUrl;
      // print('Got a message whilst in the foreground!');
      // print('Message data: ${message.data}');
      if (message.notification != null) {
        // print(
        //     'Message also contained a notification: ${message.notification!.title}');
        // Increment notification count here
        Get.find<NotificationController>().incrementCount();

        // Save notification to SharedPreferences
        saveNotification(
            message.notification!.title, message.notification!.body, imageUrl);
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      // print('Got a message when the app was opened from a notification!');
      // print('Message data: ${message.data}');
      if (message.notification != null) {
        // print(
        //     'Message also contained a notification: ${message.notification!.title}');
      }
    });
  }

  Future<void> saveNotification(String? title, String? body, imageUrl) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    List<String>? notifications = prefs.getStringList('notifications') ?? [];
    notifications
        .add(jsonEncode({'title': title, 'body': body, 'image': imageUrl}));
    await prefs.setStringList('notifications', notifications);
  }
}
