import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:zamin_phone/main.dart';
import 'package:http/http.dart' as http;
import '../../../configs/constants.dart';

class AuthApi {
  final token = prefs!.getString('token');

  Future login(request) async {
    final link = Uri.parse("${Constants().baseAuthUrl}Login");

      final response = await http.post(
        link,
        body: jsonEncode(request),
        headers: {'Content-Type': 'application/json'},
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return {"error": true, "data": jsonDecode(response.body)};
      }

  }

  Future signUp(request) async {
    final link = Uri.parse("${Constants().baseAuthUrl}SignUp");
    final response = await http.post(
      link,
      body: jsonEncode(request),
      headers: {'Content-Type': 'application/json'},
    );
    return {"code": response.statusCode, "data": response.body};
  }

  // Location
  determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return {
        'error': 'location_disabled',
        'message':
            'Location services are disabled. Please enable location services in your device settings.'
      };
    }

    // Check current permission status
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();

      if (permission == LocationPermission.denied) {
        return {
          'error': 'permission_denied',
          'message':
              'Location permissions are denied. Please grant location permission to continue.'
        };
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return {
        'error': 'permission_denied_forever',
        'message':
            'Location permissions are permanently denied. Please enable location permission in app settings.'
      };
    }

    try {
      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      await prefs!.setDouble("Latitude", position.latitude); // set the latitude
      await prefs!
          .setDouble("Longitude", position.longitude); // set the longitude

      return {'success': true, 'position': position};
    } catch (e) {
      return {
        'error': 'location_error',
        'message': 'Error getting location: $e'
      };
    }
  }

  // Helper method to open location settings
  Future<void> openLocationSettings() async {
  
      await Geolocator.openLocationSettings();
   
  }

  // Helper method to open app settings
  Future<void> openAppSettings() async {
   
      await Geolocator.openAppSettings();
   
  }

  // OTP
  getOTP(id, otp) async {
    final request = {"id": id, "otp": otp};
    final link = Uri.parse("${Constants().baseAuthUrl}Confirm");
    final response = await http.post(
      link,
      body: jsonEncode(request),
      headers: {'Content-Type': 'application/json'},
    );
    if (response.statusCode == 200) {
      return (jsonDecode(response.body));
    } else {
      return "error";
    }
  }

  resendOtp(id) async {
    final link = Uri.parse("${Constants().baseAuthUrl}ResendOtp/$id");
    final response = await http.post(
      link,
      headers: {
        'Content-Type': 'application/json',
        "Authorization": 'Bearer $token'
      },
    );
    if (response.statusCode == 200) {
      return (response.body);
    } else {
      return "error";
    }
  }

  // Change Password
  changePassword(oldPassword, newPassword) async {
    final request = {"oldPassword": oldPassword, "newPassword": newPassword};
    final link = Uri.parse("${Constants().baseClientUrl}ChangePassword");
    final response = await http.post(
      link,
      body: jsonEncode(request),
      headers: {'Content-Type': 'application/json'},
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {"error": true, "data": jsonDecode(response.body)};
    }
  }

  // Change Password
  removeAccount(password) async {
    final request = {"password": password};
    final link = Uri.parse("${Constants().baseClientUrl}RemoveAccount");
    final response = await http.post(
      link,
      body: jsonEncode(request),
      headers: {
        'Content-Type': 'application/json',
        "Authorization": 'Bearer $token'
      },
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      return {"error": true, "data": jsonDecode(response.body)};
    }
  }

  // forget password OTP
  getOtpForPassword(String phone) async {
    final link = Uri.parse("${Constants().baseAuthUrl}ForgetPassword/$phone");
    await http.post(
      link,
      headers: {
        'Content-Type': 'application/json',
      },
    );
  }

  // forget password with new password
  forgetpassword(request) async {
    final link = Uri.parse("${Constants().baseAuthUrl}ForgetPassword");
    try {
      final response = await http.put(
        link,
        body: jsonEncode(request),
        headers: {'Content-Type': 'application/json'},
      );
      if (response.statusCode == 200) {
        return {"status": true};
      } else {
        return {"status": false, "error": "Failed"};
      }
    } catch (e) {
      return {"status": false, "error": e};
    }
  }

  // Update Clint
  Future<Map<String, dynamic>> updateClient(
      String userName, String phoneNumber, accessToken) async {
    final request = {"Name": userName, "Phone": phoneNumber};
    try {
      final link = Uri.parse("${Constants().baseClientUrl}UpdateClient");

      final response = await http.put(link,
          body: jsonEncode(request),
          headers: {
            'Content-Type': 'application/json',
            "Authorization": 'Bearer $accessToken'
          });
      if (response.statusCode == 200) {
        return {"status": true, "message": "Login Succeed"};
      }
      return {"status": false, "message": "Try another Phone Number"};
    } catch (e) {
      return {"status": false, "message": "Try another Phone Number"};
    }
  }

  // phone format
  String reformatPhoneNumber(String? input, int option, [String? countryCode]) {
    // Use default country code 964 if not provided, remove + if present
    String defaultCode = (countryCode ?? '964').replaceAll('+', '');

    // Remove spaces or non-digit characters like +, -, etc.
    String cleaned = input!.replaceAll(RegExp(r'[^\d]'), '');

    // Case 1: Format as {countryCode}750xxxxxxx
    if (cleaned.startsWith('00$defaultCode')) {
      cleaned = cleaned.replaceFirst('00$defaultCode', defaultCode);
    } else if (cleaned.startsWith(defaultCode)) {
      // Already in correct country code format
    } else if (cleaned.startsWith('0')) {
      cleaned = '$defaultCode${cleaned.substring(1)}';
    } else {
      // Assume local number format, add country code
      cleaned = '$defaultCode$cleaned';
    }

    // Option 2: Format as 0750xxxxxxx
    String option2 = cleaned;
    if (option2.startsWith(defaultCode)) {
      option2 = '0${option2.substring(defaultCode.length)}';
    }

    return option == 1 ? cleaned : option2;
  }
}
