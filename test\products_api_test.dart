import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/main.dart';

void main() {
  group('ProductApi getOrders Tests', () {
    setUpAll(() async {
      // Mock shared preferences for testing
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
    });

    test('getOrders handles errors gracefully', () async {
      final productApi = ProductApi();
      
      try {
        // This will likely fail due to network issues in test environment
        final result = await productApi.getOrders();
        // If it succeeds, result should be a Data object
        expect(result, isNotNull);
      } catch (e) {
        // If it fails, it should throw an exception (not return an error map)
        expect(e, isA<Exception>());
        print('Expected exception caught: $e');
      }
    });
  });
}
