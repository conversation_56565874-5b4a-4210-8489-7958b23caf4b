import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:zamin_phone/app/models/data.dart';
import 'package:zamin_phone/app/models/history.dart';
import 'package:zamin_phone/app/models/info.dart';
import '../../../configs/constants.dart';
import '../../../main.dart';
import 'package:http/http.dart' as http;

class ProductApi {
  final token = prefs!.getString('token') ?? "";
  getOrders() async {
   
      final link = Uri.parse("${Constants().baseOrderUrl}Home");
      final response =
          await http.get(link, headers: {'Content-Type': 'application/json'});
      if (response.statusCode == 200) {
        // Check if response.body is not null and not empty before calling dataFromMap
        if (response.body.isNotEmpty) {
          return dataFromJson(response.body);
        } else {
          // Return empty Data object instead of error map
          throw Exception("Empty response body");
        }
      } else {
        // Throw exception instead of returning error map
        throw Exception("HTTP ${response.statusCode}: Failed to load data");
      }
   
  }

  Future<List<History>> getHistory(int page) async {
    final link = Uri.parse("${Constants().baseOrderUrl}History/$page");
    final response = await http.get(link, headers: {
      'Content-Type': 'application/json',
      "Authorization": 'Bearer $token'
    });
    if (response.statusCode == 200) {
      try {
        final data = (response.body);
        return historyFromJson(data);
      } catch (error) {
        // Handle decoding or parsing error
        return []; // Or return an error object
      }
    } else {
      // Handle API error
      return []; // Or return an error object
    }
  }

  getOrderById(id) async {
    final link = Uri.parse("${Constants().baseOrderUrl}Info/$id");
    final response = await http.get(link, headers: {
      'Content-Type': 'application/json',
      "Authorization": 'Bearer $token'
    });
    if (response.statusCode == 200) {
      try {
        final data = (response.body);
        return infoFromJson(data);
      } catch (error) {
        // Handle decoding or parsing error
        return []; // Or return an error object
      }
    } else {
      // Handle API error
      return []; // Or return an error object
    }
  }

  getSimilarProductName(product, products) {
    List relatedProducts = [];
    final List<String> stopWords = ['in', 'on', 'at', 'and', 'or'];
    List keywords = product.name
        .split(' ')
        .where((word) => !stopWords.contains(word))
        .toList();
    relatedProducts = products.where((a) {
      return a != product &&
          keywords.any((keyword) => a.name.contains(keyword));
    }).toList();
    return relatedProducts;
  }

  // get custom product

  getProductById(products, String productId) {
    final prod =
        products.firstWhere((element) => element.id == productId) ?? [];
    return prod;
  }

  rateProduct(productsId, rate) async {
    final link = Uri.parse("${Constants().baseRateUrl}$productsId/$rate");
    final response = await http.post(link, headers: {
      'Content-Type': 'application/json',
      "Authorization": 'Bearer $token'
    });
    if (response.statusCode == 200) {
      try {
        final data = jsonDecode(response.body);
        return data;
      } catch (error) {
        // Handle decoding or parsing error
        return []; // Or return an error object
      }
    } else {
      // Handle API error
      return "error"; // Or return an error object
    }
  }

  getProductRate(productId) async {
    final link = Uri.parse("${Constants().baseRateUrl}$productId");
    final response = await http.get(link, headers: {
      'Content-Type': 'application/json',
      "Authorization": 'Bearer $token'
    });
    if (response.statusCode == 200) {
      try {
        final data = response.body;
        return data;
      } catch (error) {
        return []; // Or return an error object
      }
    } else {
      // Handle API error
      return 0.0; // Or return an error object
    }
  }

  Future<void> sendCartDataToAPI(request) async {
    final url = Uri.parse("${Constants().baseOrderUrl}Order");
    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          "Authorization": 'Bearer $token'
        },
        body: jsonEncode({'items': request}),
      );
      if (response.statusCode == 200) {
      } else {}
    } catch (e) {
    }
  }

  cancleOrder(request) async {
    try {
      final url = Uri.parse("${Constants().baseOrderUrl}State");
      await http.put(
        url,
        headers: {
          'Content-Type': 'application/json',
          "Authorization": 'Bearer $token'
        },
        body: jsonEncode(request),
      );
    } catch (e) {
      if (kDebugMode) {
        print("Error  :  $e");
      }
    }
  }

  // Get Delivery Cost
  getDeliveryCost() async {
    final url = Uri.parse("${Constants().baseClientUrl}DeliveryCost");
    final response = await http.get(url, headers: {
      'Content-Type': 'application/json',
      "Authorization": 'Bearer $token'
    });
    if (response.statusCode == 200) {
      final result = jsonDecode(response.body);
      prefs!.setInt('deliveryCost', result['cost']);
    } else {
      // Handle API error
      prefs!.setInt('deliveryCost', 0); // Or return an error object
    }
  }
  //====================================================================

  switchName(String name) {
    if (name.isEmpty) {
      // Handle empty input string early
      return "";
    }

    List<String> names = name.split('|');
    String? selectedLanguage = prefs!.getString('lang');
    String displayText = ""; // Initialize to empty string

    switch (selectedLanguage) {
      case 'en':
        displayText = names.isNotEmpty ? names[0] : "";
        break;
      case 'ar':
        // Fallback to English (names[0]) if Kurdish part is missing or names list is too short
        displayText =
            names.length > 1 ? names[1] : (names.isNotEmpty ? names[0] : "");
        break;
      case 'so':
        displayText =
            names.length > 2 ? names[2] : (names.isNotEmpty ? names[0] : "");
        break;
      case 'ku':
        displayText =
            names.length > 3 ? names[3] : (names.isNotEmpty ? names[0] : "");
        break;
      default:
        displayText = names.isNotEmpty
            ? names[0]
            : ""; // Default to English or first available
    }

    if (displayText.isEmpty) {
      return "";
    }
    // This operation removes the last character.
    // If displayText has 1 char (e.g. "A"), result is "".
    return displayText;
  }
}
