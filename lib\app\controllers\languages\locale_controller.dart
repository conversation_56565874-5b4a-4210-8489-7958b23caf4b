import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../main.dart';

class LocaleController extends GetxController {
  Locale initialLang = prefs!.getString('lang') == "ar"
      ? const Locale('ar')
      : prefs!.getString('lang') == "ku"
          ? const Locale('ku')
          : const Locale('en');
  void changeLanguage(String language) {
    Locale locale = Locale(language);
    prefs!.setString('lang', language);
    Get.updateLocale(locale);
  }

  @override
  void onInit() {
    prefs!.setString("lang", prefs!.getString("lang") ?? "en");
    super.onInit();
  }
}
