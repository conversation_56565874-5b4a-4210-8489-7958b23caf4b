import 'dart:convert';

import 'package:get/get.dart';

import 'package:zamin_phone/app/controllers/services/products.dart';
import 'package:zamin_phone/app/models/data.dart';

import '../../../main.dart';
//import 'package:zamin_phone/app/models/product.dart';

class ProductsController extends GetxController {
  List<Category> categories = [];
  List<CategorySub> categorySubs = [];
  Data? data;
  List<Slide> slider = [];
  List<Product> products = [];
  List<OfferCategory> offerCategories = [];

  var cartItems = <String, double>{}.obs;

  @override
  void onInit() {
    super.onInit();
    getAllProducts();
    loadCartItems();
    getDeliveryCost();
  }

  // Products
  getAllProducts() async {
    try {
      final result = await ProductApi().getOrders();
      categories = result.categories;
      slider = result.slides;
      data = result;
      products = result.products;
      categorySubs = result.categorySubs;
      offerCategories = result.offerCategories;

      update();
    } catch (e) {
      // Initialize with empty lists to prevent null errors
      categories = [];
      slider = [];
      products = [];
      categorySubs = [];
      offerCategories = [];
      data = null;

      update();

      // Optionally show error to user
      // Get.snackbar("Error", "Failed to load products. Please try again.");
    }
  }

  getProductsBucategorySubId(id) {
    final result =
        products.where((element) => element.subCategoryId == id).toList();
    return result;
  }

  getSubByCategoryId(id) {
    final result =
        categorySubs.where((element) => element.categoryId == id).toList();
    return result;
  }

  // Add a product to the cart
  void addToCart(String productId, double quantity) {
    if (cartItems.containsKey(productId)) {
      cartItems[productId] = cartItems[productId]! + quantity;
    } else {
      cartItems[productId] = quantity;
    }
    saveCartItems();
  }

  // Remove a product from the cart
  void removeFromCart(String productId) {
    cartItems.remove(productId);
    saveCartItems();
  }

  // Clear the cart
  void clearCart() {
    cartItems.clear();
    prefs!.remove("cartItems");
  }

  // Get total items in the cart
  //double get totalItems => cartItems.values.fold(0, (sum, item) => sum + item);

  // Prepare data to send to the API
  List<Map<String, dynamic>> getCartData() {
    return cartItems.entries
        .map((entry) => {'viewProductId': entry.key, 'quantity': entry.value})
        .toList();
  }

  void increaseQuantity(String productId) {
    if (cartItems.containsKey(productId)) {
      cartItems[productId] = cartItems[productId]! + 1;
    }
    saveCartItems();
  }

  void decreaseQuantity(String productId) {
    if (cartItems.containsKey(productId)) {
      if (cartItems[productId]! > 1) {
        cartItems[productId] = cartItems[productId]! - 1;
      } else {
        // Remove the product if quantity becomes 0
        removeFromCart(productId);
      }
    }
    saveCartItems();
  }

  getTotalPrice(productId, quantity) {
    double totalPrice = 0;
    final product = products.firstWhere((item) => item.id == productId);
    return totalPrice + product.price * quantity;
  }

  getTotalAmount() {
    double totalAmount = 0;
    for (var entry in cartItems.entries) {
      totalAmount += getTotalPrice(entry.key, entry.value);
    }
    return totalAmount + (prefs!.getInt("deliveryCost") ?? 0);
  }

  // Save cartItems to SharedPreferences
  Future<void> saveCartItems() async {
    String cartItemsJson = jsonEncode(cartItems);
    await prefs!.setString('cartItems', cartItemsJson);
  }

  // Load cartItems from SharedPreferences
  Future<void> loadCartItems() async {
    String? cartItemsJson = prefs!.getString('cartItems');
    if (cartItemsJson != null) {
      Map<String, dynamic> decodedCartItems = jsonDecode(cartItemsJson);
      cartItems.value =
          decodedCartItems.map((key, value) => MapEntry(key, value as double));
    }
  }
  int deliveryCost = 0;
  void getDeliveryCost() async{
    await ProductApi().getDeliveryCost();
    deliveryCost = prefs!.getInt("deliveryCost") ?? 0;

    update();
  }
}
